server:
  port: 8080
  servlet:
    context-path: /ps2-auto-earning-worker

spring:
  application:
    name: ps2-auto-earning-worker
  datasource.driver-class-name: oracle.jdbc.driver.OracleDriver
  jpa:
    hibernate.ddl-auto: none
    properties.hibernate.dialect: org.hibernate.dialect.Oracle12cDialect
  datasource:
    url: ${JDBC_URL:}
    username: ${JDBC_USERNAME:}
    password: ${JDBC_PASSWORD}

sftp-server:
  protocol: sftp
  host: ${SFTP_HOST:localhost}
  port: ${SFTP_PORT:2022}
  username: ${SFTP_USERNAME:}
  password: ${SFTP_PASSWORD:}
  retry-host: ${SFTP_RETRY_HOST:localhost}
  retry-port: ${SFTP_RETRY_PORT:2022}
  retry-username: ${SFTP_RETRY_USERNAME:}
  retry-password: ${SFTP_RETRY_PASSWORD:}
  base-folder: ${SFTP_BASE_FOLDER:/reconcile/loyalty-reward/read/autoearning}
  retry-base-folder: ${SFTP_RETRY_BASE_FOLDER:/reconcile/loyalty-reward/retry/read/autoearning}
  uo-output-folder: ${SFTP_UO_OUTPUT_FOLDER:/reconcile/loyalty-reward/write/autoearning/UO}
  ut-output-folder: ${SFTP_UT_OUTPUT_FOLDER:/reconcile/loyalty-reward/write/autoearning/UT}
  known-hosts-path: ${KNOWN_HOSTS_PATH:./conf/known_hosts}
  strict-host-key-checking: no
  private-key-passphrase: ${SFTP_KEY_PASSPHRASE:}
  private-key-path: ${SFTP_PRIVATE_KEY_PATH:}
  pgp-control-enable: ${SFTP_PGP_CONTROL_ENABLE:false}
  pgp-data-enable: ${SFTP_PGP_DATA_ENABLE:false}
  pgp-retry-control-enable: ${SFTP_PGP_RETRY_CONTROL_ENABLE:false}
  pgp-retry-data-enable: ${SFTP_PGP_RETRY_DATA_ENABLE:false}
  pgp-private-key-path: ${SFTP_PGP_PRIVATE_KEY_PATH:keys/pgp_private-key.asc}
  pgp-public-key-path: ${SFTP_PGP_PUBLIC_KEY_PATH:keys/pgp_public-key.asc}
  pgp-private-key-out-path: ${SFTP_PGP_PRIVATE_KEY_OUT_PATH:keys/pgp_private-key.asc}
  pgp-uo-public-key-out-path: ${SFTP_PGP_UO_PUBLIC_KEY_OUT_PATH:keys/pfa-encrypt-public-key.pem}
  pgp-ut-public-key-out-path: ${SFTP_PGP_UT_PUBLIC_KEY_OUT_PATH:keys/encrypt_key_np.pem}
  pgp-private-key-passphrase: ${SFTP_PGP_PRIVATE_KEY_PASSPHRASE:123456}
  pgp-private-key-out-passphrase: ${SFTP_PGP_PRIVATE_KEY_OUT_PASSPHRASE:123456}
  delay-time: ${SFTP_DELAY_TIME:900000}

app:
  performance:
    threads: ${APP_PERFORMANCE_THREADS:8}
    queues: ${APP_PERFORMANCE_QUEUES:2048}
  background-process:
    max-pool-size: ${APP_PERFORMANCE_THREADS:8}
    queue-capacity:  ${APP_PERFORMANCE_QUEUES:2048}

  business:
    tcb: ${APP_BUSINESS_TCB:TCB}
    oneu: ${APP_BUSINESS_ONEU:VGC}
  program:
    tcb: ${APP_PROGRAM_TCBC:TCBC}
    oneu: ${APP_PROGRAM_ONEU:VGC}
  currency:
    base: ${APP_CURRENCY_BASE:VND}
    tcb: ${APP_CURRENCY_TCB:TPOINT}
    oneu: ${APP_CURRENCY_ONEU:Upoint}
  channel-code: ${APP_CHANNEL_CODE:AUTO_EARNING}
  corporation-code:
    auto-earn: ${APP_CORPORATION_CODE_AUTO_EARN:WEALTHCRAFTAEP}
    earn-event: ${APP_CORPORATION_CODE_EARN_EVENT:TCBLOYALTY}
  service-code:
    auto-earn: ${APP_SERVICE_CODE_AUTO_EARN:AE_TOPUP}
    earn-event: ${APP_SERVICE_CODE_EARN_EVENT:EVENT_EARNING}
  store-code:
    auto-earn: ${APP_STORE_CODE_AUTO_EARN:WEALTHCRAFTAEP}
    earn-event: ${APP_STORE_CODE_EARN_EVENT:TCB_EVENT}
  pos_code:
    auto-earn: ${APP_POS_CODE_AUTO_EARN:WEALTHCRAFTAEP}
    earn-event: ${APP_POS_CODE_EARN_EVENT:TCB_EVENT}
  end-of-month-dates: ${END_OF_MONTH_DATES:31/01/2024, 29/02/2024, 31/03/2024, 30/04/2024, 31/05/2024, 30/06/2024, 31/07/2024, 31/08/2024, 30/09/2024, 31/10/2024, 30/11/2024, 31/12/2024}
  casa-event-code: ${CASA_EVENT_CODE:120520}
  eop-event-code: ${EOP_EVENT_CODE:120533}
  product-casa-code: ${PRODUCT_CASA_CODE:RETAIL_CASA}
  casa-booster-invoice-no-format: ${CASA_BOOSTER_INVOICE_NO_FORMAT:%s-2}
  retry-transaction-page-size: ${RETRY_TRANSACTION_PAGE_SIZE:100}
  oneloyalty:
    service:
      base-url: ${OL_SERVICE_BASE_URL:https://api-qc.int.vinid.dev/oneloyalty-service}
