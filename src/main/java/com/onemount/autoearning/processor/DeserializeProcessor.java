package com.onemount.autoearning.processor;

import com.onemount.autoearning.constant.Constant;
import com.onemount.autoearning.constant.EProcessServiceId;
import com.onemount.autoearning.exception.DeserializeException;
import com.onemount.autoearning.model.ProcessData;
import com.onemount.autoearning.service.ContentProcessFactory;
import com.onemount.autoearning.service.ContentProcessService;
import com.onemount.autoearning.support.utils.Log;
import com.onemount.autoearning.support.utils.LogData;
import lombok.RequiredArgsConstructor;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class DeserializeProcessor implements Processor {

    @Override
    public void process(Exchange exchange) throws Exception {
        String processId = exchange.getIn().getHeader(Constant.Header.PROCESS_ID, String.class);
        Long fileId = exchange.getIn().getHeader(Constant.Header.DATA_FILE_ID, Long.class);
        String fileName = exchange.getIn().getHeader(Constant.Header.DATA_FILE_NAME, String.class);
        String fileHeader = exchange.getIn().getHeader(Constant.Header.FILE_HEADER, String.class);
        String lineData = exchange.getIn().getBody(String.class);

        ContentProcessService<Object> processService =
                ContentProcessFactory.getProcessor(EProcessServiceId.of(processId));

        try {
            Object parsedData = processService.deserialize(lineData, fileHeader);
            String orderKey = processService.getOrderKey(parsedData);

            exchange.getIn().setHeader(Constant.Header.ORDER_KEY, orderKey);

            ProcessData<?> processData = ProcessData.builder()
                    .processId(processId)
                    .rawData(lineData)
                    .fileId(fileId)
                    .fileName(fileName)
                    .orderKey(orderKey)
                    .dataObj(parsedData)
                    .build();

            exchange.getIn().setBody(processData);
        } catch (DeserializeException e) {
            Log.error(LogData.createLogData()
                    .append("mes", "DeserializeProcessor - Exception while deserializing file")
                    .append("data_file", fileName)
                    .append("line_data", lineData)
                    .append("error_msg", e.getMessage())
            );
            exchange.setProperty(Exchange.EXCEPTION_HANDLED, true);
            exchange.getIn().setBody(null);
        }
    }
}
