package com.onemount.autoearning.processor;

import com.oneid.oneloyalty.common.constant.EProcessingStatus;
import com.oneid.oneloyalty.common.entity.DataFileHistory;
import com.oneid.oneloyalty.common.repository.DataFileHistoryRepository;
import com.onemount.autoearning.constant.Constant;
import com.onemount.autoearning.constant.EErrorCode;
import com.onemount.autoearning.support.utils.Log;
import com.onemount.autoearning.support.utils.LogData;
import lombok.RequiredArgsConstructor;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class DataFileHistoryProcessor implements Processor {
    private final DataFileHistoryRepository dataFileHistoryRepo;

    @Override
    public void process(Exchange exchange) {
        // Do nothing
    }

    public void processStart(Exchange exchange) {
        DataFileHistory dataFileHistory = updateDataFileStatus(exchange, EProcessingStatus.PROCESSING);
        exchange.getIn().setHeader(Constant.Header.DATA_FILE_ID, dataFileHistory.getId());
    }

    public void processEnd(Exchange exchange) {
        updateDataFileStatus(exchange, EProcessingStatus.SUCCESS);
    }

    public void processError(Exchange exchange) {
        EErrorCode errorCode = exchange.getIn().getBody(EErrorCode.class);
        updateDataFileStatus(exchange, EProcessingStatus.FAILED, errorCode);
    }

    private DataFileHistory updateDataFileStatus(Exchange exchange, EProcessingStatus status) {
        return updateDataFileStatus(exchange, status, null);
    }

    private DataFileHistory updateDataFileStatus(Exchange exchange, EProcessingStatus status, EErrorCode errorCode) {

        String dataFileName = exchange.getIn().getHeader(Constant.Header.DATA_FILE_NAME, String.class);
        String controlFileName = exchange.getIn().getHeader(Constant.Header.CONTROL_FILE_NAME, String.class);

        Log.info(LogData.createLogData()
                .append("msg", "DataFileHistoryProcessor - Start update data file history status")
                .append("data_file", dataFileName)
                .append("control_file", controlFileName)
                .append("status", status.getValue())
        );

        DataFileHistory dataFileHistory = dataFileHistoryRepo.find(dataFileName, controlFileName);
        dataFileHistory.setStatus(status);
        if (errorCode != null) {
            dataFileHistory.setErrorCode(errorCode.getCode());
            dataFileHistory.setErrorMessage(errorCode.getMessage());
        } else {
            dataFileHistory.setErrorCode(null);
            dataFileHistory.setErrorMessage(null);
        }
        return dataFileHistoryRepo.save(dataFileHistory);
    }
}
