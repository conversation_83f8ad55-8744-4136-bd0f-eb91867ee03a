package com.onemount.autoearning.processor;

import com.oneid.oneloyalty.common.constant.EFileType;
import com.oneid.oneloyalty.common.constant.EProcessingStatus;
import com.oneid.oneloyalty.common.entity.ControlFileHistory;
import com.oneid.oneloyalty.common.repository.ControlFileHistoryRepository;
import com.onemount.autoearning.constant.Constant;
import lombok.RequiredArgsConstructor;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class PreProcessDataFileProcessor implements Processor {

    private final ControlFileHistoryRepository controlFileRepo;

    @Override
    public void process(Exchange exchange) throws Exception {

        String controlFileName = exchange.getIn().getHeader(Constant.Header.CONTROL_FILE_NAME, String.class);
        EFileType controlFileType = exchange.getIn().getHeader(Constant.Header.CONTROL_FILE_TYPE, EFileType.class);

        ControlFileHistory controlFileEntity = controlFileRepo.findByFileName(controlFileName, controlFileType);

        boolean skipFlag = !EProcessingStatus.PROCESSING.equals(controlFileEntity.getStatus());

        if (skipFlag) {
            exchange.setProperty(Constant.Properties.DATA_FILE_SKIP, true);
        }
    }
}