package com.onemount.autoearning.processor;

import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.EFileType;
import com.oneid.oneloyalty.common.constant.EProcessingStatus;
import com.oneid.oneloyalty.common.entity.ControlFileHistory;
import com.oneid.oneloyalty.common.entity.RetryFileRequest;
import com.oneid.oneloyalty.common.repository.ControlFileHistoryRepository;
import com.oneid.oneloyalty.common.repository.DataFileHistoryRepository;
import com.oneid.oneloyalty.common.repository.RetryFileRequestRepository;
import com.onemount.autoearning.constant.Constant;
import com.onemount.autoearning.constant.EErrorCode;
import com.onemount.autoearning.support.utils.Log;
import com.onemount.autoearning.support.utils.LogData;
import lombok.RequiredArgsConstructor;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
@RequiredArgsConstructor
public class ControlFileHistoryProcessor implements Processor {
    private final ControlFileHistoryRepository controlFileHistoryRepo;
    private final DataFileHistoryRepository dataFileHistoryRepo;
    private final RetryFileRequestRepository retryFileRequestRepo;

    @Override
    public void process(Exchange exchange) {
        // Do nothing
    }

    public void processStart(Exchange exchange) {
        String controlFileName = exchange.getIn().getHeader(Constant.Header.CONTROL_FILE_NAME, String.class);
        EFileType controlFileType = exchange.getIn().getHeader(Constant.Header.CONTROL_FILE_TYPE, EFileType.class);
        updateStatus(controlFileName, EProcessingStatus.PROCESSING, controlFileType);
    }

    public void processEnd(Exchange exchange) {
        String controlFileName = exchange.getIn().getHeader(Constant.Header.CONTROL_FILE_NAME, String.class);
        EFileType controlFileType = exchange.getIn().getHeader(Constant.Header.CONTROL_FILE_TYPE, EFileType.class);
        Long notSuccess = dataFileHistoryRepo.countFileNotSuccess(controlFileName);
        if (notSuccess == null || notSuccess <= 0) {
            updateStatus(controlFileName, EProcessingStatus.SUCCESS, controlFileType);
        }
    }

    public void processFormatError(Exchange exchange) {
        String controlFileName = exchange.getIn().getHeader(Constant.Header.CONTROL_FILE_NAME, String.class);
        EFileType controlFileType = exchange.getIn().getHeader(Constant.Header.CONTROL_FILE_TYPE, EFileType.class);
        EErrorCode errorCode = exchange.getIn().getBody(EErrorCode.class);
        if (errorCode == null) {
            errorCode = EErrorCode.FILE_FORMAT_INVALID;
        }

        ControlFileHistory ctrFileHistory = controlFileHistoryRepo.findByFileName(controlFileName, controlFileType);
        if (ctrFileHistory == null) {
            ctrFileHistory = ControlFileHistory.builder()
                    .status(EProcessingStatus.FAILED)
                    .fileName(controlFileName)
                    .totalFiles(0)
                    .isIgnored(EBoolean.NO)
                    .fileType(controlFileType)
                    .build();
        }
        ctrFileHistory.setErrorCode(errorCode.getCode());
        ctrFileHistory.setErrorMessage(errorCode.getMessage());
        controlFileHistoryRepo.save(ctrFileHistory);
        Log.error(LogData.createLogData()
                .append("mes", "ControlFileProcessor - Stop process due to exception while deserializing file")
                .append("control_file", controlFileName)
                .append("error_msg", errorCode.getMessage())
        );
    }

    public void processError(Exchange exchange) {
        String controlFileName = exchange.getIn().getHeader(Constant.Header.CONTROL_FILE_NAME, String.class);
        EFileType controlFileType = exchange.getIn().getHeader(Constant.Header.CONTROL_FILE_TYPE, EFileType.class);
        EErrorCode errorCode = exchange.getIn().getBody(EErrorCode.class);
        updateStatus(controlFileName, EProcessingStatus.FAILED, errorCode, controlFileType);
    }

    private void updateStatus(String controlFileName, EProcessingStatus status, EFileType controlFileType) {
        updateStatus(controlFileName, status, null, controlFileType);
    }

    private void updateStatus(String controlFileName, EProcessingStatus status, EErrorCode errorCode, EFileType controlFileType) {

        Log.info(LogData.createLogData()
                .append("msg", "ControlFileHistoryProcessor - Start update control file history status")
                .append("control_file", controlFileName)
                .append("status", status.getValue())
        );

        ControlFileHistory history = controlFileHistoryRepo.findByFileName(controlFileName, controlFileType);
        history.setStatus(status);
        if (errorCode != null) {
            history.setErrorCode(errorCode.getCode());
            history.setErrorMessage(errorCode.getMessage());
        } else if (EProcessingStatus.SUCCESS.equals(status)) {
            history.setErrorCode(null);
            history.setErrorMessage(null);
        }
        controlFileHistoryRepo.save(history);

        Optional<RetryFileRequest> retryFileRequestOpt = retryFileRequestRepo.findByFileName(controlFileName);
        if (retryFileRequestOpt.isPresent()) {
            RetryFileRequest retryFileRequest = retryFileRequestOpt.get();
            retryFileRequest.setStatus(status);
            retryFileRequest.setErrorCode(history.getErrorCode());
            retryFileRequest.setErrorMessage(history.getErrorMessage());
            retryFileRequestRepo.save(retryFileRequest);
        }
    }
}