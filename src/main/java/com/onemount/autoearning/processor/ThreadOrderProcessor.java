package com.onemount.autoearning.processor;

import com.oneid.oneloyalty.common.util.StringUtil;
import com.onemount.autoearning.constant.Constant;
import com.onemount.autoearning.model.ProcessData;
import lombok.RequiredArgsConstructor;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.concurrent.ThreadLocalRandom;

@Component
@RequiredArgsConstructor
public class ThreadOrderProcessor implements Processor {

    @Value("${app.performance.threads:8}")
    private Integer threadsNum;

    @Override
    public void process(Exchange exchange) {
        ProcessData<?> processData = exchange.getIn().getBody(ProcessData.class);

        int threadIndex;
        if (processData != null && StringUtil.isNotEmpty(processData.getOrderKey())) {
            String orderKey = processData.getOrderKey();
            threadIndex = Math.abs(orderKey.hashCode() % threadsNum);
        } else {
            threadIndex = ThreadLocalRandom.current().nextInt(threadsNum);
        }

        exchange.setProperty(Constant.Properties.ORDER_THREAD_INDEX, threadIndex);
    }
}
