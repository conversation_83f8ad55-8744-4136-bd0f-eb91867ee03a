package com.onemount.autoearning.processor;

import com.oneid.oneloyalty.common.constant.EFileType;
import com.onemount.autoearning.constant.Constant;
import com.onemount.autoearning.constant.EErrorCode;
import com.onemount.autoearning.exception.DataFileProcessException;
import com.onemount.autoearning.exception.FileNotFoundException;
import com.onemount.autoearning.exception.ReadFileException;
import com.onemount.autoearning.support.remote.SFTPFileSupport;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class ReadFileContentProcessor implements Processor {

    private final SFTPFileSupport sftpFileSupport;

    @Override
    public void process(Exchange exchange) {
        EFileType controlFileType = exchange.getIn().getHeader(Constant.Header.CONTROL_FILE_TYPE, EFileType.class);

        String filePath = buildFilePath(exchange);

        try {
            byte[] content = readFileContent(filePath, EFileType.RETRY_AUTO_EARNING.equals(controlFileType));
            exchange.getIn().setBody(content);
        } catch (DataFileProcessException e) {
            exchange.getIn().setBody(e);
            throw e;
        }
    }

    private String buildFilePath(Exchange exchange) {
        String sourcePath = exchange.getIn().getHeader(Exchange.FILE_PARENT, String.class);
        String fileName = exchange.getIn().getBody(String.class);
        return String.format("%s/%s", sourcePath, fileName);
    }

    private byte[] readFileContent(String filePath, boolean isRetry) {
        try {

            return sftpFileSupport.readFile(filePath, isRetry);
        } catch (ReadFileException e) {
            throw new DataFileProcessException("Exception while reading data file content", EErrorCode.READ_DATA_FILE_ERROR, e);
        } catch (FileNotFoundException e) {
            throw new DataFileProcessException("Data file not found", EErrorCode.DATA_FILE_NOT_FOUND, e);
        }
    }
}
