package com.onemount.autoearning.processor;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.onemount.autoearning.constant.Constant;
import com.onemount.autoearning.model.ControlFileData;
import com.onemount.autoearning.service.FileService;
import com.onemount.autoearning.support.remote.SFTPConfig;
import com.onemount.autoearning.support.remote.SFTPFileSupport;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@RequiredArgsConstructor
@Slf4j
public class UploadControlFileProcessor implements Processor {

    private final SFTPConfig sftpConfig;

    private final SFTPFileSupport sftpFileSupport;

    private final FileService fileService;

    @Override
    public void process(Exchange exchange) {
        List<String> uoProcessedFiles = exchange.getProperty(Constant.Properties.UO_PROCESSED_FILE, List.class);

        uploadFile(uoProcessedFiles, sftpConfig.getUoOutputFolder(), sftpConfig.getPgpUOPublicKeyOutPath());

        List<String> utProcessedFiles = exchange.getProperty(Constant.Properties.UT_PROCESSED_FILE, List.class);

        uploadFile(utProcessedFiles, sftpConfig.getUtOutputFolder(), sftpConfig.getPgpUTPublicKeyOutPath());
    }

    private void uploadFile(List<String> processedFiles, String outputFolder, String publicKeyPath) {
        if (CollectionUtils.isNotEmpty(processedFiles)) {
            ControlFileData resultControl = new ControlFileData();
            resultControl.setFileList(processedFiles);

            String result;
            ObjectMapper objectMapper = new ObjectMapper();
            try {
                result = objectMapper.writeValueAsString(resultControl);
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }

            String fileName = fileService.createJsonControlFileName();

            String filePath = String.format("%s/%s", outputFolder, fileName);
            try {
                sftpFileSupport.uploadFile(result.getBytes(), filePath, publicKeyPath, sftpConfig.getPgpControlEnable());
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
    }
}