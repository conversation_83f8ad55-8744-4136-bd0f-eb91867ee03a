package com.onemount.autoearning.processor;

import com.onemount.autoearning.constant.Constant;
import com.onemount.autoearning.constant.EProcessServiceId;
import com.onemount.autoearning.model.AutoEarningResult;
import com.onemount.autoearning.model.ProcessData;
import com.onemount.autoearning.service.ContentProcessFactory;
import com.onemount.autoearning.service.ContentProcessService;
import com.onemount.autoearning.support.utils.Log;
import com.onemount.autoearning.support.utils.LogData;
import lombok.RequiredArgsConstructor;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class ContentProcessor implements Processor {

    @Override
    @SuppressWarnings("unchecked")
    public void process(Exchange exchange) throws Exception {
        ProcessData<Object> processData = exchange.getIn().getBody(ProcessData.class);
        if (processData == null) {
            return;
        }
        try {
            String processId = exchange.getIn().getHeader(Constant.Header.PROCESS_ID, String.class);
            ContentProcessService<Object> processService = ContentProcessFactory.getProcessor(EProcessServiceId.of(processId));
            AutoEarningResult autoEarningResult = processService.process(processData);

            exchange.getIn().setBody(autoEarningResult);
        } catch (Exception ex) {
            handleException(processData, ex);
        }
    }

    /*
     * Common exception handling
     * Ensures that line processing does not throw exceptions
     */
    private void handleException(ProcessData<Object> processData, Exception ex) {
        Log.warn(LogData.createLogData()
                .append("msg", "Exception while processing a line of data file")
                .append("data_file", processData.getFileName())
                .append("raw_data", processData.getRawData())
                .append("error_msg", ex.getMessage())
        );
    }
}
