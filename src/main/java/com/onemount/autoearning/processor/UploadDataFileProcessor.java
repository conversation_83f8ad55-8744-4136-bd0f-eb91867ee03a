package com.onemount.autoearning.processor;

import com.oneid.oneloyalty.common.entity.DataFileHistory;
import com.oneid.oneloyalty.common.repository.DataFileHistoryRepository;
import com.onemount.autoearning.constant.Constant;
import com.onemount.autoearning.model.AutoEarningResult;
import com.onemount.autoearning.service.FileService;
import com.onemount.autoearning.support.remote.SFTPConfig;
import com.onemount.autoearning.support.remote.SFTPFileSupport;
import com.onemount.autoearning.support.utils.CSVUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
@Slf4j
public class UploadDataFileProcessor implements Processor {

    private final SFTPConfig sftpConfig;

    private final SFTPFileSupport sftpFileSupport;

    private final DataFileHistoryRepository dataFileHistoryRepo;

    private final FileService fileService;

    @Override
    public void process(Exchange exchange) {
        String fileName = exchange.getIn().getHeader(Constant.Header.DATA_FILE_NAME, String.class);

        List<AutoEarningResult> body = exchange.getIn().getBody(List.class);
        updateTotalRecord(exchange, body);
        try {
            if (CollectionUtils.isNotEmpty(body)) {

                // Upload UT CSV file
                List<AutoEarningResult> utBody = body.stream().filter(i -> !i.isIgnoreUt()).collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(utBody)) {
                    String utFileName = fileService.createCsvResultFileName(fileName, utBody.size());
                    String utFilePath = String.format("%s/%s", sftpConfig.getUtOutputFolder(), utFileName);
                    utFileName = sftpFileSupport.uploadFile(CSVUtil.generateAutoEarningUTCSV(body), utFilePath, sftpConfig.getPgpUTPublicKeyOutPath(), sftpConfig.getPgpDataEnable());
                    Set<String> processedFiles = exchange.getProperty(Constant.Properties.UT_PROCESSED_FILE, Set.class);
                    processedFiles.add(utFileName);
                }

                List<AutoEarningResult> uoBody = body.stream().filter(i -> !i.isIgnoreUo()).collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(uoBody)) {
                    // Upload UO CSV file
                    String uoFileName = fileService.createCsvResultFileName(fileName, uoBody.size());
                    String uoFilePath = String.format("%s/%s", sftpConfig.getUoOutputFolder(), uoFileName);
                    uoFileName = sftpFileSupport.uploadFile(CSVUtil.generateAutoEarningUOCSV(body), uoFilePath, sftpConfig.getPgpUOPublicKeyOutPath(), sftpConfig.getPgpDataEnable());
                    Set<String> processedFiles = exchange.getProperty(Constant.Properties.UO_PROCESSED_FILE, Set.class);
                    processedFiles.add(uoFileName);
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private void updateTotalRecord(Exchange exchange, List<AutoEarningResult> body) {
        String dataFileName = exchange.getIn().getHeader(Constant.Header.DATA_FILE_NAME, String.class);
        String controlFileName = exchange.getIn().getHeader(Constant.Header.CONTROL_FILE_NAME, String.class);

        DataFileHistory dataFileHistory = dataFileHistoryRepo.find(dataFileName, controlFileName);

        Integer total = exchange.getProperty(Constant.Properties.DATA_FILE_TOTAL, Integer.class);

        long totalFailedRecord = body.stream().filter(i -> Constant.TransactionStatus.PROCESSING.equals(i.getStatusUo())
                || Constant.TransactionStatus.PROCESSING.equals(i.getStatusUt())).count();

        dataFileHistory.setTotalRecord(total);
        dataFileHistory.setTotalFailedRecord(Math.toIntExact(totalFailedRecord));

        dataFileHistoryRepo.save(dataFileHistory);
    }
}