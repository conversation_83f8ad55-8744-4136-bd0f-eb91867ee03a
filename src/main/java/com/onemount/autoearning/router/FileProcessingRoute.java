package com.onemount.autoearning.router;

import com.oneid.oneloyalty.common.constant.EFileType;
import com.onemount.autoearning.constant.Constant;
import com.onemount.autoearning.constant.EErrorCode;
import com.onemount.autoearning.constant.EProcessServiceId;
import com.onemount.autoearning.exception.DataFileProcessException;
import com.onemount.autoearning.exception.DataFileValidationException;
import com.onemount.autoearning.exception.DecryptControlFileException;
import com.onemount.autoearning.exception.DecryptDataFileException;
import com.onemount.autoearning.exception.DeserializeControlFileException;
import com.onemount.autoearning.processor.ContentProcessor;
import com.onemount.autoearning.processor.ControlFileHistoryProcessor;
import com.onemount.autoearning.processor.ControlFileProcessor;
import com.onemount.autoearning.processor.DataFileHistoryProcessor;
import com.onemount.autoearning.processor.DeserializeProcessor;
import com.onemount.autoearning.processor.PreProcessControlFileProcessor;
import com.onemount.autoearning.processor.PreProcessDataFileProcessor;
import com.onemount.autoearning.processor.ReadFileContentProcessor;
import com.onemount.autoearning.processor.ThreadOrderProcessor;
import com.onemount.autoearning.processor.UploadControlFileProcessor;
import com.onemount.autoearning.processor.UploadDataFileProcessor;
import com.onemount.autoearning.strategy.DataFileAggregationStrategy;
import com.onemount.autoearning.support.remote.SFTPConfig;
import lombok.RequiredArgsConstructor;
import org.apache.camel.Exchange;
import org.apache.camel.LoggingLevel;
import org.apache.camel.builder.RouteBuilder;
import org.apache.camel.model.dataformat.PGPDataFormat;
import org.apache.http.client.utils.URIBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;

// @formatter:off
@Component
@RequiredArgsConstructor
public class FileProcessingRoute extends RouteBuilder {

    private final SFTPConfig sftpConfig;
    private final ControlFileProcessor controlFileProcessor;
    private final ReadFileContentProcessor readFileContentProcessor;
    private final ContentProcessor contentProcessor;
    private final ControlFileHistoryProcessor controlFileHistoryProcessor;
    private final DataFileHistoryProcessor dataFileHistoryProcessor;
    private final PreProcessControlFileProcessor preProcessControlFileProcessor;
    private final PreProcessDataFileProcessor preProcessDataFileProcessor;
    private final DeserializeProcessor deserializeProcessor;
    private final ThreadOrderProcessor threadOrderProcessor;
    private final UploadDataFileProcessor uploadDataFileProcessor;
    private final UploadControlFileProcessor uploadControlFileProcessor;

    @Value("${app.performance.threads:8}")
    private Integer threadsNum;
    @Value("${app.performance.queues:1000}")
    private Integer queueSize;

    // Used to wait for the data file processing to complete
    private final Map<String, CountDownLatch> countDownLatchMap = new ConcurrentHashMap<>();

    @Override
    public void configure() throws Exception {

        PGPDataFormat pgpFormat = pgpEncrypt();

        /*
            Process flow

            1. Listen for a new control file
                - Read the control file and extract the list of data files into the body.

            2.Move to file processing
                - Iterate through the sorted list of data files and process them sequentially.

            3. Decrypt each data file using PGP and read its content.
            4. Split the file content into lines using tokenization.
            5. Process each line in parallel for optimized performance.
         */

        /*
         *  Exception decrypt control file
         */
        onException(DecryptControlFileException.class)
                .handled(true)
                .setBody(constant(EErrorCode.FILE_DECRYPT_FAILED))
                .process(controlFileHistoryProcessor::processFormatError)
                .stop();

        /*
         *  Exception deserialize control file
         */
        onException(DeserializeControlFileException.class)
                .handled(true)
                .setBody(constant(EErrorCode.FILE_FORMAT_INVALID))
                .process(controlFileHistoryProcessor::processFormatError)
                .stop();

        /*
         *  Exception verify list data file
         */
        onException(DataFileValidationException.class)
                .handled(true)
                .setBody(constant(EErrorCode.DATA_FILE_ERROR))
                .process(controlFileHistoryProcessor::processError)
                .stop();

        /*
         *  Exception decrypt data file
         */
        onException(DecryptDataFileException.class)
                .handled(true)
                .setBody(constant(EErrorCode.FILE_DECRYPT_FAILED))
                .process(dataFileHistoryProcessor::processError)
                .setBody(constant(EErrorCode.PROCESS_DATA_FILE_FAILED))
                .process(controlFileHistoryProcessor::processError)
                .stop();

        /*
         *  Exception process data file
         */
        onException(DataFileProcessException.class)
                .handled(true)
                .process( exchange -> {
                    DataFileProcessException exception
                            = exchange.getProperty(Exchange.EXCEPTION_CAUGHT, DataFileProcessException.class);
                    if(exception != null && exception.getErrorCode() != null){
                        exchange.getIn().setBody(exception.getErrorCode());
                    }else {
                        exchange.getIn().setBody(EErrorCode.PROCESS_DATA_FILE_FAILED);
                    }
                })
                .process(dataFileHistoryProcessor::processError)
                .setBody(constant(EErrorCode.PROCESS_DATA_FILE_FAILED))
                .process(controlFileHistoryProcessor::processError)
                .stop();

        /*
         *  General exception
         */
        onException(Exception.class)
                .handled(true)
                .log(LoggingLevel.ERROR, "Exception while processing file: ${header.CamelFileName}")
                .log(LoggingLevel.ERROR, "StackTrace:\n ${exception.stacktrace}")
                .stop();

        /*
            Route to process the Control File
            Listens for new files from the server and reprocesses pending or failed control files
         */
        from(sftpURI(sftpConfig.getBaseFolder()).toString())
                .routeId("process-control-file")
                .process(exchange -> {
                    exchange.getIn().setHeader(Constant.Header.CONTROL_FILE_TYPE, constant(EFileType.AUTO_EARNING));
                    exchange.setProperty(Constant.Properties.UO_PROCESSED_FILE, new HashSet<>());
                    exchange.setProperty(Constant.Properties.UT_PROCESSED_FILE, new HashSet<>());
                })
                .choice()
                    .when(simple("${header.CamelFileNameOnly} regex '^ae-control-\\d{8}-\\d{13}\\..*$'"))
                    .process(preProcessControlFileProcessor)
                    .choice()
                        .when(exchange -> exchange.getProperty(Constant.Properties.CONTROL_FILE_SKIP) == null)
                        .log("Start processing control file: ${header.CamelFileNameOnly}")
                        .choice()
                            .when(simple("${properties:sftp-server.pgp-control-enable} == true"))
                                .log("Decrypting control file: ${header.CamelFileNameOnly}")
                                .doTry()
                                    .unmarshal(pgpFormat)
                                .doCatch(Exception.class)
                                    .throwException(DecryptControlFileException.class,"")
                                .end()
                        .end()
                        .process(e -> countDownLatchMap.clear())                                                        // Reset count down map
                        .process(controlFileProcessor)                                                                  // Process control file content
                        .process(controlFileHistoryProcessor::processStart)                                             // Update control file processing
                        .split(body())                                                                                  // Split list data files
                        .setHeader(Constant.Header.DATA_FILE_NAME, body())                                              // Set data file name to Header
                        .to("direct:processFile")                                                                   // Move to route process data file
                    .end()
                    .to("direct:generateResultControl")
                    .process(controlFileHistoryProcessor::processEnd)                                                   // Update control file status
                    .process(this::cleanupBody)
                .stop();

        /*
             Route to process the Data file
             Check and forward data to the corresponding route for processing
         */
        from("direct:processFile")
                .routeId("processFileRoute")
                .process(preProcessDataFileProcessor)                      // Check whether the data should proceed to processing or be skipped
                .choice()
                    .when(exchange -> exchange.getProperty(Constant.Properties.DATA_FILE_SKIP) == null)
                        .log("Start processing data file: ${header." + Constant.Header.DATA_FILE_NAME + "}")
                        .process(dataFileHistoryProcessor::processStart)
                        .choice()
                            .when(body().regex("^ae-\\d{8}-\\d{0,19}-\\d{0,19}-\\d{13}\\..*$"))
                                .to("direct:earning")
                        .end()
                        .to("direct:waitForFileProcessing")
                        .process(dataFileHistoryProcessor::processEnd)
                        .log("End process data file: ${header." + Constant.Header.DATA_FILE_NAME + "}")
                        .process(this::cleanupAll)
                    .end()
                .end();

        /*
            Route to handle each data type individually
         */
        from("direct:earning")
                .setHeader(Constant.Header.PROCESS_ID, constant(EProcessServiceId.AUTO_EARNING.getName()))
                .to("direct:decryptAndProcess");

        /*
             Route to process the Data file
             - Decrypt file
             - Read content & process logic
         */
        from("direct:decryptAndProcess")
                .process(readFileContentProcessor)
                .choice()
                    .when(simple("${properties:sftp-server.pgp-data-enable} == true"))
                        .log("Decrypting data file: ${header." + Constant.Header.DATA_FILE_NAME + "}")
                        .doTry()
                            .unmarshal(pgpFormat)
                        .doCatch(Exception.class)
                            .log(LoggingLevel.ERROR, "Exception occurred while decrypting file ${header."
                                    + Constant.Header.DATA_FILE_NAME + "}: ${exception.message}\n${exception.stacktrace}")
                            .throwException(DecryptDataFileException.class,"")
                        .end()
                        .log("End decrypt data file: ${header." + Constant.Header.DATA_FILE_NAME + "}")
                .end()
                .process(exchange -> {
                    String content = exchange.getIn().getBody(String.class);
                    String[] lines = content.split("\n");
                    String dataFileName = exchange.getIn().getHeader(Constant.Header.DATA_FILE_NAME, String.class);
                    // TODO: store column name
                    exchange.getIn().setHeader(Constant.Header.FILE_HEADER, lines[0]);
                    CountDownLatch latch = new CountDownLatch(lines.length - 1);
                    countDownLatchMap.put(dataFileName, latch);
                    exchange.getIn().setBody(Arrays.copyOfRange(lines, 1, lines.length));
                    exchange.setProperty(Constant.Properties.DATA_FILE_TOTAL, lines.length - 1);
                })
                .split(body()).streaming()
                .process(deserializeProcessor)
                .process(threadOrderProcessor)
                .toD("seda:processLine-${exchangeProperty." + Constant.Properties.ORDER_THREAD_INDEX + "}" + "?blockWhenFull=true&size=" + queueSize);

        /*
            Worker pool to receive and process each line of the data file
         */
        for (int i = 0; i < threadsNum; i++) {
            from("seda:processLine-" + i + "?blockWhenFull=true&size=" + queueSize)
                    .process(contentProcessor)
                    .to("direct:processOutput")
                    .process(exchange -> {
                        String fileName = exchange.getIn().getHeader(Constant.Header.DATA_FILE_NAME, String.class);
                        CountDownLatch latch = countDownLatchMap.get(fileName);
                        if (latch != null) {
                            latch.countDown();
                        }
                    })
                    .process(this::cleanupBody)
            ;
        }

        /*
            Route to handle waiting data file completed
         */
        from("direct:waitForFileProcessing")
                .process(exchange -> {
                    String fileName = exchange.getIn().getHeader(Constant.Header.DATA_FILE_NAME, String.class);
                    CountDownLatch latch = countDownLatchMap.get(fileName);
                    if (latch != null) {
                        latch.await();
                        countDownLatchMap.remove(fileName);
                    }
                });

        // Process output
        from("direct:processOutput")
                .aggregate(simple("${header." + Constant.Header.DATA_FILE_NAME + "}"), new DataFileAggregationStrategy())
                .completionSize(simple("${exchangeProperty." + Constant.Properties.DATA_FILE_TOTAL + "}"))
                .log(LoggingLevel.INFO, "Pushing file: ${header." + Constant.Header.DATA_OUTPUT_FILE_NAME + "} with body: ${body}")
                .process(uploadDataFileProcessor)
                .log(LoggingLevel.INFO, "Successfully pushed file: ${header." + Constant.Header.DATA_OUTPUT_FILE_NAME + "}")
                .process(this::cleanupBody)
        ;

        // Generate and upload control files
        from("direct:generateResultControl")
                .process(uploadControlFileProcessor)

        ;
    }

    private URI sftpURI(String folderPath) throws URISyntaxException {

        URIBuilder sftpURIBuilder = new URIBuilder()
                .setScheme(sftpConfig.getProtocol())
                .setHost(sftpConfig.getHost())
                .setPort(Integer.parseInt(sftpConfig.getPort()))
                .setPath(folderPath)
                .addParameter("username", sftpConfig.getUsername())
                .addParameter("password", "RAW(" + sftpConfig.getPassword() + ")")
                .addParameter("delay", sftpConfig.getDelayTime())
                .addParameter("maxDepth", "0")
                .addParameter("recursive", "true")
                .addParameter("idempotent", "false")
                .addParameter("streamDownload", "true")
                .addParameter("preSort", "true")
                .addParameter("knownHostsFile", sftpConfig.getKnownHostsPath())
                .addParameter("noop", "true")
                .addParameter("stepwise", "false");

        if (sftpConfig.getPrivateKeyPath() != null && !sftpConfig.getPrivateKeyPath().isEmpty()) {
            sftpURIBuilder.addParameter("privateKeyFile", sftpConfig.getPrivateKeyPath());
        }

        if (sftpConfig.getPrivateKeyPassphrase() != null && !sftpConfig.getPrivateKeyPassphrase().isEmpty()) {
            sftpURIBuilder.addParameter("privateKeyPassphrase", sftpConfig.getPrivateKeyPassphrase());
        }

        return sftpURIBuilder.build();
    }

    public PGPDataFormat pgpEncrypt() {
        PGPDataFormat pgp = new PGPDataFormat();
        pgp.setKeyFileName("file:" + sftpConfig.getPgpPrivateKeyPath());
        pgp.setSignatureKeyFileName("file:" + sftpConfig.getPgpPublicKeyPath());
        pgp.setPassword(sftpConfig.getPgpPrivateKeyPassphrase());
        pgp.setArmored("true");
        pgp.setIntegrity("true");

        return pgp;
    }

    private void cleanupBody(Exchange exchange) {
        exchange.getIn().setBody(null);
    }

    private void cleanupAll(Exchange exchange) {
        exchange.getIn().setBody(null);
        exchange.getIn().getHeaders().clear();
        exchange.getProperties().clear();
    }
}