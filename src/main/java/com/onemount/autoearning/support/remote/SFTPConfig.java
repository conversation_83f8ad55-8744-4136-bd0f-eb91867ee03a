package com.onemount.autoearning.support.remote;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

@Data
@ConfigurationProperties(prefix = "sftp-server")
public class SFTPConfig {

    private String protocol;

    private String host;

    private String port;

    private String username;

    private String password;

    private String retryHost;

    private String retryPort;

    private String retryUsername;

    private String retryPassword;

    private String baseFolder;

    private String retryBaseFolder;

    private String uoOutputFolder;

    private String utOutputFolder;

    private String knownHostsPath;

    private String strictHostKeyChecking;

    private String privateKeyPassphrase;

    private String privateKeyPath;

    private Boolean pgpControlEnable;

    private Boolean pgpDataEnable;

    private Boolean pgpRetryControlEnable;

    private Boolean pgpRetryDataEnable;

    private String pgpPrivateKeyPath;

    private String pgpPublicKeyPath;

    private String pgpPrivateKeyOutPath;

    private String pgpUOPublicKeyOutPath;

    private String pgpUTPublicKeyOutPath;

    private String pgpPrivateKeyPassphrase;

    private String pgpPrivateKeyOutPassphrase;

    private String delayTime;
}
