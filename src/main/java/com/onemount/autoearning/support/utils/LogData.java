package com.onemount.autoearning.support.utils;

import java.util.HashMap;

@SuppressWarnings("rawtypes")
public class LogData extends HashMap {

    private static final long serialVersionUID = 402280888469310269L;

    public static LogData createLogData() {
        return new LogData();
    }

    @SuppressWarnings("unchecked")
    public LogData append(LogKey key, Object value) {
        put(key.getValue(), value);
        return this;
    }

    @SuppressWarnings("unchecked")
    public LogData append(String key, Object value) {
        put(key, value);
        return this;
    }

    public enum LogKey {
        CLIENT_ID("client_id"),
        ACTION("action"),
        USER_ID("user_id"),
        REQ_URL("req_url"),
        REQ_METHOD("req_method"),
        REQ_PARAM("req_param"),
        REQ_DATA("req_data"),
        RESP_DATA("resp_data"),
        ERR_DATA("err_data"),
        ERR_CODE("err_code"),
        ERR_MSG("err_msg"),
        HTTP_STATUS("http_status"),
        EXEC_DURATION("exec_duration");

        private final String value;

        LogKey(String value) {
            this.value = value;
        }

        public String getValue() {
            return this.value;
        }
    }
}
