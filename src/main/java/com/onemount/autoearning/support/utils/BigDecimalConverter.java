package com.onemount.autoearning.support.utils;

import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Objects;

public class BigDecimalConverter {
    public static BigDecimal toBigDecimal(String src) {
        if (StringUtils.isEmpty(src)) {
            return null;
        }
        try {
            return new BigDecimal(src);
        } catch (Exception e) {
            return null;
        }
    }

    public static boolean isValid(String src) {
        return StringUtils.isEmpty(src) || Objects.nonNull(toBigDecimal(src));
    }
}
