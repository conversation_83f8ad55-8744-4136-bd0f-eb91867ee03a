package com.onemount.autoearning.support.utils;

import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

@Component
public class FileNameGenerator {

    private static final String CSV_RESULT_FILE_FORMAT = "ae-result-%s-%s-%d-%d.csv";
    private static final String JSON_CONTROL_FILE_FORMAT = "ae-result-control-%s-%d.json";
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");

    public String generateFileName(String originalFileName, FileNameParams params) {
        if (params.getFileType() == FileType.CSV) {
            return generateCsvResultFileName(originalFileName, params.getTotalRecords());
        } else if (params.getFileType() == FileType.JSON) {
            return generateJsonControlFileName();
        }
        throw new IllegalArgumentException("Unsupported file type: " + params.getFileType());
    }

    private String generateCsvResultFileName(String originalFileName, long totalRecords) {
        String orderOfFile = extractOrderOfFile(originalFileName);
        String date = LocalDate.now().format(DATE_FORMATTER);
        long timestamp = Instant.now().toEpochMilli();

        return String.format(CSV_RESULT_FILE_FORMAT, date, orderOfFile, totalRecords, timestamp);
    }

    private String generateJsonControlFileName() {
        String date = LocalDate.now().format(DATE_FORMATTER);
        long timestamp = Instant.now().toEpochMilli();

        return String.format(JSON_CONTROL_FILE_FORMAT, date, timestamp);
    }

    private String extractOrderOfFile(String fileName) {
        String[] parts = fileName.split("-");
        if (parts.length >= 3) {
            return parts[2]; // order_of_file is the third part
        }
        throw new IllegalArgumentException("Cannot extract order from filename: " + fileName);
    }

    public enum FileType {
        CSV,
        JSON
    }

    @Data
    @AllArgsConstructor
    public static class FileNameParams {
        private long totalRecords;
        private FileType fileType;
    }
}