package com.onemount.autoearning.support.utils;

import com.onemount.autoearning.model.AutoEarningResult;
import com.opencsv.CSVWriter;

import java.io.StringWriter;
import java.util.List;

public final class CSVUtil {

    public static byte[] generateAutoEarningUTCSV(List<AutoEarningResult> autoEarningUTS) throws Exception {
        StringWriter stringWriter = new StringWriter();
        try (CSVWriter writer = new CSVWriter(stringWriter)) {
            String[] header = {"oneu_id", "loyalty_cust_id", "txn_amount", "txn_id", "loy_txn_ref_uo", "status_uo", "award_point_uo", "loy_txn_ref_ut", "status_ut", "award_point_ut"};
            writer.writeNext(header);

            for (AutoEarningResult autoEarningUT : autoEarningUTS) {
                if (!autoEarningUT.isIgnoreUt()) {
                    String[] data = {
                            autoEarningUT.getOneuId(),
                            autoEarningUT.getLoyaltyCustId(),
                            autoEarningUT.getTxnAmount(),
                            autoEarningUT.getTxnId(),
                            autoEarningUT.getLoyTxnRefUo(),
                            autoEarningUT.getStatusUo(),
                            autoEarningUT.getAwardPointUo(),
                            autoEarningUT.getLoyTxnRefUt(),
                            autoEarningUT.getStatusUt(),
                            autoEarningUT.getAwardPointUt(),
                    };
                    writer.writeNext(data);
                }
            }
        }
        return stringWriter.toString().getBytes();
    }

    public static byte[] generateAutoEarningUOCSV(List<AutoEarningResult> autoEarningUOS) throws Exception {
        StringWriter stringWriter = new StringWriter();
        try (CSVWriter writer = new CSVWriter(stringWriter)) {
            String[] header = {"oneu_id", "loyalty_cust_id", "txn_amount", "txn_id", "loy_txn_ref_uo", "status_uo", "award_point_uo"};
            writer.writeNext(header);

            for (AutoEarningResult autoEarningUO : autoEarningUOS) {
                if (!autoEarningUO.isIgnoreUo()) {
                    String[] data = {
                            autoEarningUO.getOneuId(),
                            autoEarningUO.getLoyaltyCustId(),
                            autoEarningUO.getTxnAmount(),
                            autoEarningUO.getTxnId(),
                            autoEarningUO.getLoyTxnRefUo(),
                            autoEarningUO.getStatusUo(),
                            autoEarningUO.getAwardPointUo(),
                    };
                    writer.writeNext(data);
                }
            }
        }
        return stringWriter.toString().getBytes();
    }
}