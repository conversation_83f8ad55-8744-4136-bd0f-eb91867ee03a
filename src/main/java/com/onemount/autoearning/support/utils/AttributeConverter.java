package com.onemount.autoearning.support.utils;

import com.oneid.oneloyalty.client.model.attributes.TransactionAttributeReq;
import com.onemount.autoearning.constant.EAttributeType;
import com.onemount.autoearning.support.DateConverter;

public class AttributeConverter {

    public static TransactionAttributeReq toTxnAttribute(EAttributeType type, String code, String src) {
        String value;

        switch (type) {
            case DATE:
                value = DateConverter.toDateAttributeValue(src);
                break;
            case DATE_TIME:
                value = DateConverter.toDateTimeAttributeValue(src);
                break;
            default: // TEXT, NUMBER
                value = src;
        }

        TransactionAttributeReq attribute = new TransactionAttributeReq();
        attribute.setCode(code);
        attribute.setValue(value);

        return attribute;
    }
}