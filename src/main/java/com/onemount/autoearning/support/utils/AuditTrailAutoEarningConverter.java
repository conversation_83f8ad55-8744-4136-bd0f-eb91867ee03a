package com.onemount.autoearning.support.utils;

import com.oneid.oneloyalty.common.constant.ETransactionStatus;
import com.oneid.oneloyalty.common.entity.AuditTrailAutoEarning;
import com.oneid.oneloyalty.common.util.StringUtil;
import com.onemount.autoearning.constant.Constant;
import com.onemount.autoearning.model.AutoEarningResult;

import java.math.BigDecimal;

public class AuditTrailAutoEarningConverter {

    public static AuditTrailAutoEarning from(Long dataFileId, String ftTransactionId, AutoEarningResult autoEarningRes) {
        AuditTrailAutoEarning autoEarning = new AuditTrailAutoEarning();
        autoEarning.setDataFileId(dataFileId);
        autoEarning.setOneuId(autoEarningRes.getOneuId());
        autoEarning.setLoyaltyCustId(autoEarningRes.getLoyaltyCustId());
        autoEarning.setTransactionId(autoEarningRes.getTxnId());
        autoEarning.setTransactionAmount(BigDecimal.valueOf(Long.parseLong(autoEarningRes.getTxnAmount())));
        autoEarning.setFtTransactionId(ftTransactionId);
        autoEarning.setOneuTransactionRef(autoEarningRes.getLoyTxnRefUo());
        autoEarning.setAwardPointUo(!StringUtil.isNotEmpty(autoEarningRes.getAwardPointUo()) ? BigDecimal.ZERO : BigDecimal.valueOf(Long.parseLong(autoEarningRes.getAwardPointUo())));
        autoEarning.setOneuStatus(Constant.TransactionStatus.SUCCESS.equals(autoEarningRes.getStatusUo()) ? ETransactionStatus.SUCCESS : ETransactionStatus.FAIL);
        autoEarning.setOneuErrorMessage(autoEarningRes.getErrorMessageUo());
        autoEarning.setTcbTransactionRef(autoEarningRes.getLoyTxnRefUt());
        autoEarning.setAwardPointUt(!StringUtil.isNotEmpty(autoEarningRes.getAwardPointUt()) ? BigDecimal.ZERO : BigDecimal.valueOf(Long.parseLong(autoEarningRes.getAwardPointUt())));
        autoEarning.setTcbStatus(Constant.TransactionStatus.SUCCESS.equals(autoEarningRes.getStatusUt()) ? ETransactionStatus.SUCCESS : ETransactionStatus.FAIL);
        autoEarning.setTcbErrorMessage(autoEarningRes.getErrorMessageUt());

        return autoEarning;
    }
}