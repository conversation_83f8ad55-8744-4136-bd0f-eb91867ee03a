package com.onemount.autoearning.support.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public final class Log {
    private static final Logger INSTANCE = LoggerFactory.getLogger("APP_LOGGER");
    private static String APP_MODE;

    private Log() {
    }

    public static Logger get() {
        return INSTANCE;
    }

    public static void info(LogData data) {
        INSTANCE.info(JsonUtil.writeValueAsString(data));
    }

    public static void info(String data) {
        INSTANCE.info(data);
    }

    public static void error(LogData data) {
        INSTANCE.error(JsonUtil.writeValueAsString(data));
    }

    public static void error(String data) {
        INSTANCE.error(data);
    }

    public static void error(String data, Throwable e) {
        INSTANCE.error(data, e);
    }

    public static void warn(LogData data) {
        INSTANCE.warn(JsonUtil.writeValueAsString(data));
    }

    public static void warn(String data) {
        INSTANCE.warn(data);
    }

    public static void debug(LogData data) {
        INSTANCE.debug(JsonUtil.writeValueAsString(data));
    }

    public static void debug(String data) {
        INSTANCE.debug(data);
    }
}
