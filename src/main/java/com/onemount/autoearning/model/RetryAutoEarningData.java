package com.onemount.autoearning.model;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class RetryAutoEarningData extends AutoEarningData implements Serializable {

    private static final long serialVersionUID = 5801302780182860079L;

    private String loyTxnRefUo;

    private String awardPointUo;
}