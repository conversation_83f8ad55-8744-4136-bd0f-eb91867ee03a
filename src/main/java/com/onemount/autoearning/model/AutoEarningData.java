package com.onemount.autoearning.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.onemount.autoearning.constant.ErrorCode;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class AutoEarningData implements Serializable {

    private static final long serialVersionUID = 5801302780182860079L;

    private String dsPartitionDate;

    private String oneuId;

    private String loyaltyCustId;

    private String txnId;

    private String txnAmount;

    private String ftTxnId;

    @JsonIgnore
    private ErrorCode errorCode;
}
