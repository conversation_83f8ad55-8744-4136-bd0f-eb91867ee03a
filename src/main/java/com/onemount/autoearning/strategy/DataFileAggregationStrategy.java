package com.onemount.autoearning.strategy;

import com.onemount.autoearning.model.AutoEarningResult;
import org.apache.camel.AggregationStrategy;
import org.apache.camel.Exchange;

import java.util.ArrayList;
import java.util.List;

public class DataFileAggregationStrategy implements AggregationStrategy {

    @Override
    public Exchange aggregate(Exchange oldExchange, Exchange newExchange) {
        if (oldExchange == null) {
            List<Object> records = new ArrayList<>();
            AutoEarningResult autoEarningResult = newExchange.getIn().getBody(AutoEarningResult.class);
            if (autoEarningResult != null) records.add(autoEarningResult);
            newExchange.getIn().setBody(records);
            return newExchange;
        }

        List<Object> oldRecords = oldExchange.getIn().getBody(List.class);
        AutoEarningResult autoEarningResult = newExchange.getIn().getBody(AutoEarningResult.class);
        if (autoEarningResult != null) oldRecords.add(autoEarningResult);
        oldExchange.getIn().setBody(oldRecords);
        return oldExchange;
    }
}