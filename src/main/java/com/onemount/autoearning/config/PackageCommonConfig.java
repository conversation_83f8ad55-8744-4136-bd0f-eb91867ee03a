package com.onemount.autoearning.config;

import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

@Configuration
@EntityScan(basePackages = {"com.oneid.oneloyalty.common.entity"})
@EnableJpaRepositories("com.oneid.oneloyalty.common.repository")
public class PackageCommonConfig {
}