package com.onemount.autoearning.config;

import com.oneid.oneloyalty.client.service.ServiceClient;
import com.oneid.oneloyalty.client.service.ServiceConfigParams;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

@Configuration
public class OneLoyaltyClientConfig {

    @Value("${app.oneloyalty.service.base-url:}")
    public String serviceUrl;

    @Bean
    public ServiceClient serviceClient() {
        ServiceConfigParams configParams = new ServiceConfigParams(serviceUrl);
        return new ServiceClient(new RestTemplate(), configParams);
    }
}
