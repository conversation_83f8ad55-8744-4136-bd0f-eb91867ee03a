package com.onemount.autoearning.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;

@Configuration
@EnableAsync
public class ThreadPoolConfig {
    @Value("${app.background-process.core-pool-size:2}")
    public Integer backgroundProcessCorePoolSize;

    @Value("${app.background-process.max-pool-size:8}")
    public Integer backgroundProcessMaxPoolSize;

    @Value("${app.background-process.queue-capacity:2048}")
    public Integer backgroundProcessQueueCapacity;

    @Value("${app.background-process.thread-name-prefix:backgroundProcess-}")
    public String backgroundProcessThreadNamePrefix;

    @Bean(name = "backgroundProcessThreadPool")
    public Executor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(backgroundProcessCorePoolSize);
        executor.setMaxPoolSize(backgroundProcessMaxPoolSize);
        executor.setQueueCapacity(backgroundProcessQueueCapacity);
        executor.setThreadNamePrefix(backgroundProcessThreadNamePrefix);
        executor.initialize();
        return executor;
    }
}
