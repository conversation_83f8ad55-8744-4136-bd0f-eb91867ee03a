package com.onemount.autoearning.constant;

import com.fasterxml.jackson.annotation.JsonValue;

import java.util.HashMap;
import java.util.Map;

public enum EProcessServiceId {
    AUTO_EARNING("auto-earning-process-service"),
    RETRY_AUTO_EARNING("retry-auto-earning-process-service"),
    ;

    private static final Map<String, EProcessServiceId> mapById;

    static {
        mapById = new HashMap<>();
        for (EProcessServiceId e : values()) {
            mapById.put(e.getName(), e);
        }
    }

    EProcessServiceId(String id) {
        this.name = id;
    }

    private final String name;

    @JsonValue
    public String getName() {
        return name;
    }

    public static EProcessServiceId of(String name) {
        return mapById.get(name);
    }
}