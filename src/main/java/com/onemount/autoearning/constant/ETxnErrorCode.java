package com.onemount.autoearning.constant;

import com.fasterxml.jackson.annotation.JsonValue;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

public enum ETxnErrorCode {
    SUCCESS("000", "SUCCESS"),
    SYSTEM_ERROR("001", "SYSTEM_ERROR"),
    LOYALTY_CUS_ID_NOT_FOUND("002","LOYALTY_CUS_ID_NOT_FOUND"),
    TXN_VALIDATE_ERROR("003","TXN_VALIDATE_ERROR"),
    TXN_TIME_FORMAT_ERROR("004", "TXN_TIME_FORMAT_ERROR"),
    AMOUNT_ERROR("005","AMOUNT_ERROR"),
    MCC_ERROR("006","MCC_ERROR"),
    TXN_NOT_PASS_POLICY("007","TXN_NOT_PASS_POLICY"),
    CARD_TYPE_ERROR("008","CARD_TYPE_ERROR"),
    DUPLICATE_INVOICE_NO_ERROR("009","DUPLICATE_INVOICE_NO_ERROR");

    private static final Map<String, ETxnErrorCode> mapByValue;

    private static final Map<EErrorCode, ETxnErrorCode> mapByErrorCode;

    static {
        mapByValue = new HashMap<>();
        for (ETxnErrorCode e : values()) {
            mapByValue.put(e.getCode(), e);
        }
    }

    ETxnErrorCode(String value, String message) {
        this.code = value;
        this.message = message;
    }

    private final String code;

    private final String message;

    @JsonValue
    public String getCode() {
        return this.code;
    }

    public static ETxnErrorCode of(String value) {
        return mapByValue.get(value);
    }

    public String getMessage() {
        return this.message;
    }

    public static ETxnErrorCode convert(EErrorCode errorCode) {
        ETxnErrorCode tcbErrorCode = mapByErrorCode.get(errorCode);
        return Objects.nonNull(tcbErrorCode) ? tcbErrorCode : ETxnErrorCode.SYSTEM_ERROR;
    }

    static {
        mapByErrorCode = new HashMap<>();

        mapByErrorCode.put(EErrorCode.SUCCESS, ETxnErrorCode.SUCCESS);

        mapByErrorCode.put(EErrorCode.UNKNOWN, ETxnErrorCode.SYSTEM_ERROR);

        // Validation
        mapByErrorCode.put(EErrorCode.PAYLOAD_CANNOT_NULL, ETxnErrorCode.TXN_VALIDATE_ERROR);
        mapByErrorCode.put(EErrorCode.TIMESTAMP_CANNOT_NULL, ETxnErrorCode.TXN_VALIDATE_ERROR);
        mapByErrorCode.put(EErrorCode.LOYALTY_CUS_ID_CANNOT_EMPTY, ETxnErrorCode.TXN_VALIDATE_ERROR);

        mapByErrorCode.put(EErrorCode.TXN_LIST_CANNOT_EMPTY, ETxnErrorCode.TXN_VALIDATE_ERROR);
        mapByErrorCode.put(EErrorCode.TRANSACTION_ID_CANNOT_EMPTY, ETxnErrorCode.TXN_VALIDATE_ERROR);
        mapByErrorCode.put(EErrorCode.ARRANGEMENT_ID_CANNOT_EMPTY, ETxnErrorCode.TXN_VALIDATE_ERROR);
//        mapByErrorCode.put(EErrorCode.CARD_ID_CANNOT_EMPTY, ETxnErrorCode.TXN_VALIDATE_ERROR);
        mapByErrorCode.put(EErrorCode.TXN_CODE_CANNOT_EMPTY, ETxnErrorCode.TXN_VALIDATE_ERROR);
        mapByErrorCode.put(EErrorCode.TXN_TYPE_CANNOT_EMPTY, ETxnErrorCode.TXN_VALIDATE_ERROR);
        mapByErrorCode.put(EErrorCode.TXN_CCY_CODE_CANNOT_EMPTY, ETxnErrorCode.TXN_VALIDATE_ERROR);
        mapByErrorCode.put(EErrorCode.TXN_DATE_CANNOT_EMPTY, ETxnErrorCode.TXN_VALIDATE_ERROR);
        mapByErrorCode.put(EErrorCode.TXN_SLR_IND_CANNOT_EMPTY, ETxnErrorCode.TXN_VALIDATE_ERROR);
        mapByErrorCode.put(EErrorCode.TXN_SLR_IND_INVALID, ETxnErrorCode.TXN_VALIDATE_ERROR);
        mapByErrorCode.put(EErrorCode.TXN_TIME_CANNOT_EMPTY, ETxnErrorCode.TXN_VALIDATE_ERROR);
        mapByErrorCode.put(EErrorCode.TXN_GROSS_AMT_CANNOT_EMPTY, ETxnErrorCode.TXN_VALIDATE_ERROR);
        mapByErrorCode.put(EErrorCode.TXN_NET_AMT_CANNOT_EMPTY, ETxnErrorCode.TXN_VALIDATE_ERROR);
        mapByErrorCode.put(EErrorCode.TXN_STATUS_CANNOT_EMPTY, ETxnErrorCode.TXN_VALIDATE_ERROR);
        mapByErrorCode.put(EErrorCode.TXN_STATUS_INVALID, ETxnErrorCode.TXN_VALIDATE_ERROR);
        mapByErrorCode.put(EErrorCode.TXN_APPCODE_CANNOT_EMPTY, ETxnErrorCode.TXN_VALIDATE_ERROR);
        mapByErrorCode.put(EErrorCode.TXN_TOKEN_CANNOT_EMPTY, ETxnErrorCode.TXN_VALIDATE_ERROR);
        mapByErrorCode.put(EErrorCode.TXN_BIN_CANNOT_EMPTY, ETxnErrorCode.TXN_VALIDATE_ERROR);

        mapByErrorCode.put(EErrorCode.TXN_QR_MERCHANT_IND_CANNOT_EMPTY, ETxnErrorCode.TXN_VALIDATE_ERROR);
        mapByErrorCode.put(EErrorCode.TXN_QR_MERCHANT_IND_INVALID, ETxnErrorCode.TXN_VALIDATE_ERROR);

        mapByErrorCode.put(EErrorCode.TXN_DATE_INVALID, ETxnErrorCode.TXN_TIME_FORMAT_ERROR);
        mapByErrorCode.put(EErrorCode.TXN_TIME_INVALID, ETxnErrorCode.TXN_TIME_FORMAT_ERROR);

        mapByErrorCode.put(EErrorCode.TXN_GROSS_AMT_INVALID, ETxnErrorCode.AMOUNT_ERROR);
        mapByErrorCode.put(EErrorCode.TXN_NET_AMT_INVALID, ETxnErrorCode.AMOUNT_ERROR);
        mapByErrorCode.put(EErrorCode.TXN_FOREIGN_TXN_AMT_INVALID, ETxnErrorCode.AMOUNT_ERROR);

        // Validate Length
        mapByErrorCode.put(EErrorCode.LOYALTY_CUS_ID_LENGTH_INVALID, ETxnErrorCode.TXN_VALIDATE_ERROR);
        mapByErrorCode.put(EErrorCode.TRANSACTION_ID_LENGTH_INVALID, ETxnErrorCode.TXN_VALIDATE_ERROR);
        mapByErrorCode.put(EErrorCode.CARD_ID_LENGTH_INVALID, ETxnErrorCode.TXN_VALIDATE_ERROR);
        mapByErrorCode.put(EErrorCode.TXN_CODE_LENGTH_INVALID, ETxnErrorCode.TXN_VALIDATE_ERROR);
        mapByErrorCode.put(EErrorCode.TXN_TYPE_LENGTH_INVALID, ETxnErrorCode.TXN_VALIDATE_ERROR);
        mapByErrorCode.put(EErrorCode.TXN_IN_OUT_LENGTH_INVALID, ETxnErrorCode.TXN_VALIDATE_ERROR);
        mapByErrorCode.put(EErrorCode.TXN_CHANNEL_LV1_LENGTH_INVALID, ETxnErrorCode.TXN_VALIDATE_ERROR);
        mapByErrorCode.put(EErrorCode.TXN_CHANNEL_LV2_LENGTH_INVALID, ETxnErrorCode.TXN_VALIDATE_ERROR);
        mapByErrorCode.put(EErrorCode.TXN_CHANNEL_LV3_LENGTH_INVALID, ETxnErrorCode.TXN_VALIDATE_ERROR);
        mapByErrorCode.put(EErrorCode.TXN_MOTHER_CATEGORY_LENGTH_INVALID, ETxnErrorCode.TXN_VALIDATE_ERROR);
        mapByErrorCode.put(EErrorCode.TXN_MCC_LENGTH_INVALID, ETxnErrorCode.TXN_VALIDATE_ERROR);
        mapByErrorCode.put(EErrorCode.TXN_SERVICE_LV1_LENGTH_INVALID, ETxnErrorCode.TXN_VALIDATE_ERROR);
        mapByErrorCode.put(EErrorCode.TXN_CCY_CODE_LENGTH_INVALID, ETxnErrorCode.TXN_VALIDATE_ERROR);
        mapByErrorCode.put(EErrorCode.TXN_ADJ_REASON_LENGTH_INVALID, ETxnErrorCode.TXN_VALIDATE_ERROR);
        mapByErrorCode.put(EErrorCode.TXN_DATE_LENGTH_INVALID, ETxnErrorCode.TXN_VALIDATE_ERROR);
        mapByErrorCode.put(EErrorCode.TXN_SERVICE_LV2_LENGTH_INVALID, ETxnErrorCode.TXN_VALIDATE_ERROR);
        mapByErrorCode.put(EErrorCode.TXN_PURPOSE_LENGTH_INVALID, ETxnErrorCode.TXN_VALIDATE_ERROR);
        mapByErrorCode.put(EErrorCode.TXN_MERCHANT_PURPOSE_LENGTH_INVALID, ETxnErrorCode.TXN_VALIDATE_ERROR);
        mapByErrorCode.put(EErrorCode.TXN_SUPPLIER_LENGTH_INVALID, ETxnErrorCode.TXN_VALIDATE_ERROR);
        mapByErrorCode.put(EErrorCode.TXN_TIME_LENGTH_INVALID, ETxnErrorCode.TXN_VALIDATE_ERROR);
        mapByErrorCode.put(EErrorCode.TXN_GROSS_AMT_LENGTH_INVALID, ETxnErrorCode.TXN_VALIDATE_ERROR);
        mapByErrorCode.put(EErrorCode.TXN_NET_AMT_LENGTH_INVALID, ETxnErrorCode.TXN_VALIDATE_ERROR);
        mapByErrorCode.put(EErrorCode.TXN_TERMINALNAME_LENGTH_INVALID, ETxnErrorCode.TXN_VALIDATE_ERROR);
        mapByErrorCode.put(EErrorCode.TXN_BRANDNAME_LENGTH_INVALID, ETxnErrorCode.TXN_VALIDATE_ERROR);
        mapByErrorCode.put(EErrorCode.TXN_TID_LENGTH_INVALID, ETxnErrorCode.TXN_VALIDATE_ERROR);
        mapByErrorCode.put(EErrorCode.TXN_MID_LENGTH_INVALID, ETxnErrorCode.TXN_VALIDATE_ERROR);
        mapByErrorCode.put(EErrorCode.TXN_TERMLOCATION_LENGTH_INVALID, ETxnErrorCode.TXN_VALIDATE_ERROR);
        mapByErrorCode.put(EErrorCode.TXN_FOREIGN_AMT_LENGTH_INVALID, ETxnErrorCode.TXN_VALIDATE_ERROR);
        mapByErrorCode.put(EErrorCode.TXN_ADJ_SIGN_LENGTH_INVALID, ETxnErrorCode.TXN_VALIDATE_ERROR);
        mapByErrorCode.put(EErrorCode.TXN_ORIGINAL_ID_LENGTH_INVALID, ETxnErrorCode.TXN_VALIDATE_ERROR);
        mapByErrorCode.put(EErrorCode.TXN_BIN_LENGTH_INVALID, ETxnErrorCode.TXN_VALIDATE_ERROR);
        mapByErrorCode.put(EErrorCode.ARRANGEMENT_ID_LENGTH_INVALID, ETxnErrorCode.TXN_VALIDATE_ERROR);

        // Business
        mapByErrorCode.put(EErrorCode.TRANSACTION_ID_DUPLICATED, ETxnErrorCode.DUPLICATE_INVOICE_NO_ERROR);
        mapByErrorCode.put(EErrorCode.MESSAGE_ID_DUPLICATED, ETxnErrorCode.DUPLICATE_INVOICE_NO_ERROR);
        mapByErrorCode.put(EErrorCode.INVOICE_NO_EXISTED, ETxnErrorCode.DUPLICATE_INVOICE_NO_ERROR);

        mapByErrorCode.put(EErrorCode.LOYALTY_CUSTOMER_NOT_FOUND, ETxnErrorCode.LOYALTY_CUS_ID_NOT_FOUND);

        mapByErrorCode.put(EErrorCode.NOT_PASS_ANY_SCHEME, ETxnErrorCode.TXN_NOT_PASS_POLICY);
    }
}
