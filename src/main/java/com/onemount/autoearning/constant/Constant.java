package com.onemount.autoearning.constant;

public interface Constant {

    String CREATED_BY_SYSTEM = "SYSTEM";

    class Header {
        public static final String PROCESS_ID = "process-id";
        public static final String DATA_FILE_NAME = "DataFileName";
        public static final String DATA_FILE_ID = "DataFileId";
        public static final String CONTROL_FILE_ID = "ControlFileId";
        public static final String CONTROL_FILE_NAME = "ControlFileName";
        public static final String FILE_LIST = "FileList";
        public static final String ORDER_KEY = "OrderKey";
        public static final String DATA_LINE = "DataLine";
        public static final String PARSED_DATA = "ParsedData";
        public static final String DATA_FILE_FIRST = "DataFileFirst";
        public static final String DATA_FILE_LAST = "DataFileLast";
        public static final String FILE_HEADER = "FileHeader";
        public static final String DATA_OUTPUT_FILE_NAME = "DataOutputFileName";
        public static final String CONTROL_OUTPUT_FILE_NAME = "ControlOutputFileName";
        public static final String CONTROL_FILE_TYPE = "ControlFileType";
    }

    class Properties {
        public static final String CONTROL_FILE_SKIP = "ControlFileSkip";
        public static final String DATA_FILE_SKIP = "DataFileSkip";
        public static final String ORDER_THREAD_INDEX = "OrderThreadIndex";
        public static final String DATA_FILE_TOTAL = "DataFileTotal";
        public static final String UO_PROCESSED_FILE = "UOProcessedFiles";
        public static final String UT_PROCESSED_FILE = "UTProcessedFiles";
    }

    class TransactionStatus {
        public static final String SUCCESS = "SUCCESS";
        public static final String PROCESSING = "PROCESSING";
    }
}