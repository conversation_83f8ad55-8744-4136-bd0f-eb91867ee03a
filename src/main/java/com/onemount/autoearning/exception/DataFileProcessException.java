package com.onemount.autoearning.exception;

import com.onemount.autoearning.constant.EErrorCode;
import lombok.Getter;

/*
 * Wrap exception while processing a data file.
 * Handles file read errors, decryption errors, etc.
 */
@Getter
public class DataFileProcessException extends RuntimeException {

    private final EErrorCode errorCode;

    public DataFileProcessException(String message, EErrorCode errorCode) {
        super(message);
        this.errorCode = errorCode;
    }

    public DataFileProcessException(String message, EErrorCode errorCode, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }
}
