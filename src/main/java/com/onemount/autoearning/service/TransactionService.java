package com.onemount.autoearning.service;

import com.oneid.oneloyalty.client.model.TransactionEarnRes;
import com.onemount.autoearning.model.AutoEarningData;

public interface TransactionService {
    TransactionEarnRes earnUO(AutoEarningData autoEarning, String requestId);

    TransactionEarnRes earnUT(AutoEarningData autoEarning, String requestId, String fileName);

    void auditTrail(AutoEarningData autoEarning, TransactionEarnRes earnRes, String requestId,
                    String fileName, Long eventDate);
}