package com.onemount.autoearning.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.onemount.autoearning.exception.DeserializeException;
import com.onemount.autoearning.service.DeserializeService;
import com.onemount.autoearning.support.utils.Log;
import com.onemount.autoearning.support.utils.LogData;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
@RequiredArgsConstructor
public class DeserializeServiceImpl implements DeserializeService {

    private final ObjectMapper objectMapper;

    @Override
    public <T> T deserializeJson(String data, Class<T> tClass) {
        try {
            return objectMapper.readValue(data, tClass);
        } catch (JsonProcessingException e) {
            throw new DeserializeException("Exception while deserialize data", data, e);
        }
    }

    @Override
    public <T> T deserializeCsv(String data, String header, Class<T> tClass) {
        try {
            String[] headers = header.split(",");
            String[] values = data.split(",");

            if (headers.length != values.length) {
                Log.info(LogData.createLogData()
                        .append("msg", "Header and data length mismatch")
                        .append("autoEarning", values));
            }

            Map<String, String> fieldMap = new HashMap<>();
            for (int i = 0; i < headers.length; i++) {
                fieldMap.put(headers[i].trim(), values[i].trim());
            }

            String jsonString = objectMapper.writeValueAsString(fieldMap);
            return objectMapper.readValue(jsonString, tClass);
        } catch (Exception e) {
            throw new DeserializeException("Exception while deserialize data", data, e);
        }
    }
}