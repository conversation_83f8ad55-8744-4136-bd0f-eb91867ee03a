package com.onemount.autoearning.service.impl;

import com.oneid.oneloyalty.client.model.SchemesRes;
import com.oneid.oneloyalty.client.model.TransactionEarnRes;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.AuditTrailAutoEarning;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.repository.AuditTrailAutoEarningRepository;
import com.onemount.autoearning.constant.Constant;
import com.onemount.autoearning.constant.EProcessServiceId;
import com.onemount.autoearning.model.AutoEarningResult;
import com.onemount.autoearning.model.ProcessData;
import com.onemount.autoearning.model.RetryAutoEarningData;
import com.onemount.autoearning.service.ContentProcessService;
import com.onemount.autoearning.service.DeserializeService;
import com.onemount.autoearning.service.TransactionService;
import com.onemount.autoearning.support.utils.AuditTrailAutoEarningConverter;
import com.onemount.autoearning.support.utils.Log;
import com.onemount.autoearning.support.utils.LogData;
import com.onemount.autoearning.support.utils.StringUtil;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

@Service
@RequiredArgsConstructor
public class RetryAutoEarningProcessService implements ContentProcessService<RetryAutoEarningData> {

    private final AuditTrailAutoEarningRepository auditTrailAutoEarningRepository;
    private final TransactionService transactionService;
    private final DeserializeService deserializeService;

    @Value("${app.currency.tcb}")
    private String tcbCurrencyCode;
    @Value("${app.currency.oneu}")
    private String oneuCurrencyCode;

    @Override
    public EProcessServiceId getProcessId() {
        return EProcessServiceId.RETRY_AUTO_EARNING;
    }

    @Override
    public String getOrderKey(RetryAutoEarningData data) {
        if (Objects.nonNull(data)) {
            return data.getLoyaltyCustId();
        }
        return null;
    }

    @Override
    public RetryAutoEarningData deserialize(String data, String header) {
        return deserializeService.deserializeCsv(data, header, RetryAutoEarningData.class);
    }

    @Override
    public AutoEarningResult process(ProcessData<RetryAutoEarningData> processData) {
        String requestId = UUID.randomUUID().toString();
        RetryAutoEarningData autoEarning = processData.getDataObj();
        Log.info(LogData.createLogData()
                .append("msg", "RetryAutoEarningProcessService - data")
                .append("autoEarning", autoEarning));

        AutoEarningResult autoEarningRes = new AutoEarningResult();
        autoEarningRes.setIgnoreUo(true);
        autoEarningRes.setIgnoreUt(true);

        if (StringUtils.isNotBlank(autoEarning.getAwardPointUo())) {
            autoEarningRes.setStatusUo(Constant.TransactionStatus.SUCCESS);
            autoEarningRes.setAwardPointUo(autoEarning.getAwardPointUo());
            autoEarningRes.setLoyTxnRefUo(autoEarning.getLoyTxnRefUo());
        } else {
            autoEarningRes.setStatusUo(Constant.TransactionStatus.PROCESSING);
        }

        autoEarningRes.setOneuId(autoEarning.getOneuId());
        autoEarningRes.setLoyaltyCustId(autoEarning.getLoyaltyCustId());
        autoEarningRes.setTxnAmount(autoEarning.getTxnAmount());
        autoEarningRes.setTxnId(autoEarning.getTxnId());

        try {
            verify(autoEarning);

            TransactionEarnRes earnRes = null;
            BigDecimal point;
            if (Constant.TransactionStatus.PROCESSING.equals(autoEarningRes.getStatusUo())) {
                earnRes = transactionService.earnUO(autoEarning, requestId);
                if (earnRes.isSuccess()) {
                    autoEarningRes.setIgnoreUo(false);
                    autoEarningRes.setLoyTxnRefUo(earnRes.getData().getTransaction().getTxnRefNo());
                    autoEarningRes.setStatusUo(Constant.TransactionStatus.SUCCESS);
                    point = calPoint(earnRes.getData().getTransaction().getAwardSchemes());
                    autoEarningRes.setAwardPointUo(point.toString());
                }
            }

            autoEarningRes.setStatusUt(Constant.TransactionStatus.PROCESSING);

            if (earnRes == null || earnRes.isSuccess()) {

                earnRes = transactionService.earnUT(autoEarning, requestId, processData.getFileName());

                if (earnRes.isSuccess()) {
                    autoEarningRes.setIgnoreUt(false);
                    autoEarningRes.setLoyTxnRefUt(earnRes.getData().getTransaction().getTxnRefNo());
                    autoEarningRes.setStatusUt(Constant.TransactionStatus.SUCCESS);
                    point = calPoint(earnRes.getData().getTransaction().getAwardSchemes());
                    autoEarningRes.setAwardPointUt(point.toString());
                } else {
                    throw new BusinessException(earnRes.getMeta().getCode(), earnRes.getMeta().getMessage(), earnRes);
                }
            } else {
                throw new BusinessException(earnRes.getMeta().getCode(), earnRes.getMeta().getMessage(), earnRes);
            }
        } catch (Exception e) {
            if (Constant.TransactionStatus.PROCESSING.equals(autoEarningRes.getStatusUo())) {
                autoEarningRes.setErrorMessageUo(e.getMessage());
            }

            if (Constant.TransactionStatus.PROCESSING.equals(autoEarningRes.getStatusUt())) {
                autoEarningRes.setErrorMessageUt(e.getMessage());
            }

            Log.error(e.getMessage(), e);
        }

        AuditTrailAutoEarning auditTrailAutoEarning = AuditTrailAutoEarningConverter
                .from(processData.getFileId(), autoEarning.getFtTxnId(), autoEarningRes);

        auditTrailAutoEarningRepository.save(auditTrailAutoEarning);

        return autoEarningRes;
    }

    private void verify(RetryAutoEarningData autoEarning) {
        if (!StringUtil.isNotEmpty(autoEarning.getOneuId())
                || !StringUtil.isNotEmpty(autoEarning.getLoyaltyCustId())
                || !StringUtil.isNotEmpty(autoEarning.getTxnId())
                || !StringUtil.isNotEmpty(autoEarning.getTxnAmount())
                || !StringUtil.isNotEmpty(autoEarning.getFtTxnId())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "Missing params", null);
        }
    }

    private BigDecimal calPoint(List<SchemesRes> schemesRes) {
        return schemesRes.stream()
                .filter(i -> tcbCurrencyCode.equals(i.getCurrencyCode()) || oneuCurrencyCode.equals(i.getCurrencyCode()))
                .map(SchemesRes::getPoint)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
}