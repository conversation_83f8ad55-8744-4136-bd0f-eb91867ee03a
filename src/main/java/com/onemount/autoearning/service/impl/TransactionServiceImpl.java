package com.onemount.autoearning.service.impl;

import com.oneid.oneloyalty.client.model.AbstractRes;
import com.oneid.oneloyalty.client.model.TransactionEarnRes;
import com.oneid.oneloyalty.client.model.TransactionEarnServiceReq;
import com.oneid.oneloyalty.client.model.TransactionReq;
import com.oneid.oneloyalty.client.model.attributes.TransactionAttributeReq;
import com.oneid.oneloyalty.client.service.ServiceClient;
import com.oneid.oneloyalty.common.constant.EIdType;
import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.KafkaAuditTrailEarnEvent;
import com.oneid.oneloyalty.common.model.CustomerIdentify;
import com.oneid.oneloyalty.common.repository.KafkaAuditTrailEarnEventRepository;
import com.onemount.autoearning.constant.EAttributeType;
import com.onemount.autoearning.constant.EErrorCode;
import com.onemount.autoearning.constant.EEventErrorCode;
import com.onemount.autoearning.constant.MessageType;
import com.onemount.autoearning.constant.TxnStatus;
import com.onemount.autoearning.model.AutoEarningData;
import com.onemount.autoearning.service.TransactionService;
import com.onemount.autoearning.support.DateConverter;
import com.onemount.autoearning.support.utils.AttributeConverter;
import com.onemount.autoearning.support.utils.DateTimeConverter;
import com.onemount.autoearning.support.utils.Log;
import com.onemount.autoearning.support.utils.LogData;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Service
@RequiredArgsConstructor
public class TransactionServiceImpl implements TransactionService {

    private static final String FT_TXN_ID = "FT_TXN_ID";
    private static final String CREATED_BY_SYSTEM = "SYSTEM";
    private static final String EVENT_CODE = "120560";
    private static final String EVENT_GROUP = "12";
    private static final String EVENT_TYPE = "1205";
    private static final String EVENT_PRODUCT = "All";

    private final ServiceClient serviceClient;
    private final KafkaAuditTrailEarnEventRepository kafkaAuditTrailEarnEventRepository;

    @Value("${app.business.oneu}")
    private String oneuBizCode;
    @Value("${app.business.tcb}")
    private String tcbBizCode;
    @Value("${app.program.oneu}")
    private String oneuProgCode;
    @Value("${app.program.tcb}")
    private String tcbProgCode;
    @Value("${app.service-code.auto-earn}")
    private String autoServiceCode;
    @Value("${app.service-code.earn-event}")
    private String eventServiceCode;
    @Value("${app.corporation-code.auto-earn}")
    private String autoCorpCode;
    @Value("${app.corporation-code.earn-event}")
    private String eventCorpCode;
    @Value("${app.store-code.auto-earn}")
    private String autoStoreCode;
    @Value("${app.store-code.earn-event}")
    private String eventStoreCode;
    @Value("${app.pos_code.auto-earn}")
    private String autoPosCode;
    @Value("${app.pos_code.earn-event}")
    private String eventPosCode;
    @Value("${app.currency.base}")
    private String baseCurrencyCode;
    @Value("${app.currency.tcb}")
    private String tcbCurrencyCode;
    @Value("${app.currency.oneu}")
    private String oneuCurrencyCode;
    @Value("${app.channel-code}")
    private String channelCode;

    @Override
    public TransactionEarnRes earnUO(AutoEarningData autoEarning, String requestId) {
        CustomerIdentify identify = new CustomerIdentify(autoEarning.getOneuId(), EIdType.USER_ID);

        List<TransactionAttributeReq> attributes = new ArrayList<>();
        attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, FT_TXN_ID, autoEarning.getFtTxnId()));

        TransactionEarnServiceReq req = toEarnReq(oneuBizCode, oneuProgCode, autoServiceCode, autoCorpCode,
                autoStoreCode, autoPosCode, autoEarning, identify, attributes);

        return serviceClient.earnV2(req, requestId);
    }

    @Override
    public TransactionEarnRes earnUT(AutoEarningData autoEarning, String requestId, String fileName) {
        CustomerIdentify identify = new CustomerIdentify(autoEarning.getLoyaltyCustId(), EIdType.PARTNER_CUSTOMER_ID);

        List<TransactionAttributeReq> attributes = toAttributes(autoEarning);
        attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, FT_TXN_ID, autoEarning.getFtTxnId()));

        TransactionEarnServiceReq req = toEarnReq(tcbBizCode, tcbProgCode, eventServiceCode, eventCorpCode,
                eventStoreCode, eventPosCode, autoEarning, identify, attributes);

        TransactionEarnRes earnRes = null;
        try {
            earnRes = serviceClient.earnV2(req, requestId);
            auditTrail(autoEarning, earnRes, requestId, fileName, req.getTransaction().getTransactionTime());
        } catch (Exception e) {
            if (earnRes == null) {
                AbstractRes.Meta meta = new AbstractRes.Meta();
                meta.setCode(ErrorCode.SERVER_ERROR.getValue());
                meta.setMessage("Internal server error");

                earnRes = new TransactionEarnRes();
                earnRes.setMeta(meta);
            }

            auditTrail(autoEarning, earnRes, requestId, fileName, req.getTransaction().getTransactionTime());
            throw e;
        }

        return earnRes;
    }

    @Override
    public void auditTrail(AutoEarningData autoEarning, TransactionEarnRes earnRes, String requestId, String fileName, Long eventDate) {
        try {
            KafkaAuditTrailEarnEvent auditTrail = buildAuditTrail(autoEarning, earnRes, requestId, fileName, eventDate);

            Log.info(LogData.createLogData()
                    .append("msg", "EarnEventHandlerImpl -- auditTrail")
                    .append("event_id", auditTrail.getEventId())
                    .append("error_message", auditTrail.getErrorMessage())
            );
            kafkaAuditTrailEarnEventRepository.save(auditTrail);
            Log.info("EarnEventHandlerImpl -- process auditTrail successfully");
        } catch (Exception ex) {
            Log.error("EarnEventHandlerImpl -- process auditTrail error", ex);
        }
    }

    private TransactionEarnServiceReq toEarnReq(String businessCode, String programCode, String serviceCode,
                                                String corpCode, String storeCode, String posCode,
                                                AutoEarningData autoEarning, CustomerIdentify customerIdentify,
                                                List<TransactionAttributeReq> attributes) {
        BigDecimal grossAmount = new BigDecimal(autoEarning.getTxnAmount());

        return TransactionEarnServiceReq.builder()
                .customerIdentifier(customerIdentify)
                .businessCode(businessCode)
                .programCode(programCode)
                .currencyCode(baseCurrencyCode)
                .channelCode(channelCode)
                .serviceCode(serviceCode)
                .transaction(
                        TransactionReq.builder()
                                .invoiceNo(autoEarning.getTxnId())
                                .gmv(grossAmount)
                                .grossAmount(grossAmount)
                                .transactionTime(Instant.now().getEpochSecond())
                                .corporationCode(corpCode)
                                .storeId(storeCode)
                                .posCode(posCode)
                                .attributes(attributes)
                                .build()
                )
                .build();
    }

    private List<TransactionAttributeReq> toAttributes(AutoEarningData autoEarning) {

        List<TransactionAttributeReq> attributes = new ArrayList<>();

        attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "EVENT_CODE", EVENT_CODE));

        attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "EVENT_NAME", EVENT_CODE));

        attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "EVENT_GROUP", EVENT_GROUP));

        attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "EVENT_PRODUCT", EVENT_PRODUCT));

        attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.NUMBER, "EVENT_AMOUNT", autoEarning.getTxnAmount()));

        attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "EVENT_TYPE", EVENT_TYPE));

        return attributes;
    }

    private KafkaAuditTrailEarnEvent buildAuditTrail(
            AutoEarningData autoEarning,
            TransactionEarnRes earnRes,
            String requestId,
            String fileName,
            Long eventDate
    ) {
        KafkaAuditTrailEarnEvent auditTrail = new KafkaAuditTrailEarnEvent();
        auditTrail.setCreatedAt(new Date());
        auditTrail.setCreatedBy(CREATED_BY_SYSTEM);
        auditTrail.setMessageId(requestId);
        auditTrail.setMessageType(MessageType.TRANS_EARN_POINT_REQ.getValue());
        auditTrail.setTimestamp(eventDate);
        auditTrail.setBusinessCode(tcbBizCode);
        auditTrail.setProgramCode(tcbProgCode);
        auditTrail.setCurrencyCode(baseCurrencyCode);
        auditTrail.setCorporationCode(eventCorpCode);
        auditTrail.setStoreCode(eventStoreCode);
        auditTrail.setPosCode(eventPosCode);
        auditTrail.setDsPartitionDate(StringUtils.isNotBlank(autoEarning.getDsPartitionDate()) ? DateConverter.toDateFormatterYYYYMMDD(autoEarning.getDsPartitionDate()) : null);
        auditTrail.setFileName(fileName);

        auditTrail.setLoyaltyCusId(autoEarning.getLoyaltyCustId());
        auditTrail.setEventId(autoEarning.getTxnId());
        auditTrail.setEventCode(EVENT_CODE);
        auditTrail.setEventName(EVENT_CODE);
        auditTrail.setEventGroup(EVENT_GROUP);
        auditTrail.setEventProduct(EVENT_PRODUCT);
        auditTrail.setEventAmount(autoEarning.getTxnAmount());
        auditTrail.setEventDate(DateTimeConverter.toString(eventDate));
        auditTrail.setEventType(EVENT_TYPE);
        auditTrail.setServiceCode(eventServiceCode);

        EErrorCode errorCode = null;
        EEventErrorCode tcbErrorCode = null;

        if (Objects.nonNull(earnRes) && Objects.nonNull(earnRes.getData()) && Objects.nonNull(earnRes.getData().getTransaction())) {
            auditTrail.setTxnRefNo(earnRes.getData().getTransaction().getTxnRefNo());
            auditTrail.setInvoiceNo(earnRes.getData().getTransaction().getInvoiceNo());
            auditTrail.setAwardPoint(earnRes.getData().getTransaction().getAwardedPoint());
            auditTrail.setTransactionTime(DateTimeConverter.toString(earnRes.getData().getTransaction().getTransactionTime()));
            auditTrail.setTransactionStatus(TxnStatus.SUCCESS);
        } else {
            auditTrail.setTransactionStatus(TxnStatus.FAILED);
            if (earnRes != null) {
                auditTrail.setServiceErrorCode(String.valueOf(earnRes.getMeta().getCode()));
                auditTrail.setServiceErrorMessage(earnRes.getMeta().getMessage());
                errorCode = EErrorCode.convert(Integer.valueOf(auditTrail.getServiceErrorCode()));
            }
        }

        if (Objects.nonNull(errorCode)) {
            auditTrail.setErrorCode(errorCode.getCode());
            auditTrail.setErrorMessage(errorCode.getMessage());
            tcbErrorCode = EEventErrorCode.convert(errorCode);
        }

        if (Objects.nonNull(tcbErrorCode)) {
            auditTrail.setTcbErrorCode(tcbErrorCode.getCode());
            auditTrail.setTcbErrorMessage(tcbErrorCode.getMessage());
        }

        return auditTrail;
    }
}