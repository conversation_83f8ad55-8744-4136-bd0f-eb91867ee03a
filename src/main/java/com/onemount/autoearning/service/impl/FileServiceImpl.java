package com.onemount.autoearning.service.impl;

import com.onemount.autoearning.service.FileService;
import com.onemount.autoearning.support.utils.FileNameGenerator;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class FileServiceImpl implements FileService {

    private final FileNameGenerator fileNameGenerator;

    @Override
    public String createCsvResultFileName(String originalFileName, long totalRecords) {
        FileNameGenerator.FileNameParams params =
                new FileNameGenerator.FileNameParams(totalRecords, FileNameGenerator.FileType.CSV);
        return fileNameGenerator.generateFileName(originalFileName, params);
    }

    @Override
    public String createJsonControlFileName() {
        FileNameGenerator.FileNameParams params =
                new FileNameGenerator.FileNameParams(0, FileNameGenerator.FileType.JSON);
        return fileNameGenerator.generateFileName(null, params);
    }
}