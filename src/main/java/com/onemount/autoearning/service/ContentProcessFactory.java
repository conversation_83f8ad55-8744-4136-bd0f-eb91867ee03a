package com.onemount.autoearning.service;

import com.onemount.autoearning.constant.EProcessServiceId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class ContentProcessFactory {

    private static final Map<EProcessServiceId, ContentProcessService<?>> masterJobMap = new HashMap<>();

    @Autowired
    private ContentProcessFactory(List<ContentProcessService<?>> services) {
        for (ContentProcessService<?> service : services) {
            masterJobMap.put(service.getProcessId(), service);
        }
    }

    @SuppressWarnings("unchecked")
    public static <T> ContentProcessService<T> getProcessor(EProcessServiceId jobName) {
        ContentProcessService<T> service = (ContentProcessService<T>) masterJobMap.get(jobName);
        if (service == null) {
            throw new RuntimeException("Process service not found for " + jobName);
        }
        return service;
    }
}
