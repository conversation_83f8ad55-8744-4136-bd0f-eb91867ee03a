package com.onemount.autoearning.service;

import com.onemount.autoearning.constant.EProcessServiceId;
import com.onemount.autoearning.exception.DeserializeException;
import com.onemount.autoearning.model.AutoEarningResult;
import com.onemount.autoearning.model.ProcessData;

/**
 * <AUTHOR>
 */
public interface ContentProcessService<T> {

    AutoEarningResult process(ProcessData<T> processData);

    EProcessServiceId getProcessId();

    T deserialize(String data, String header) throws DeserializeException;

    String getOrderKey(T parsedData);
}
