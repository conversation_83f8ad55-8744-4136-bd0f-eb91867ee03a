apiVersion: v1
kind: ConfigMap
metadata:
  name: ${CI_PROJECT_NAME}
data:
  ENVIRONMENT: "${ENVIRONMENT}"
  APP_BUSINESS_CODE: "TCB"
  APP_PROGRAM_CODE: "TCBC"
  SFTP_HOST: "**************"
  SFTP_PORT: "2022"
  SFTP_RETRY_HOST: "**************"
  SFTP_RETRY_PORT: "2022"
  SFTP_BASE_FOLDER: "/reconcile/loyalty-reward/read/autoearning"
  SFTP_UO_OUTPUT_FOLDER: "/reconcile/loyalty-reward/write/autoearning/UO"
  SFTP_UT_OUTPUT_FOLDER: "/reconcile/loyalty-reward/write/autoearning/UT"
  KNOWN_HOSTS_PATH: "./conf/known_hosts"
#  SFTP_PRIVATE_KEY_PATH: "./keys/tcb_ssh_private_key_dev.pem"
  SFTP_PGP_ENABLE: "false"
  OL_SERVICE_BASE_URL: "https://api-qc.int.vinid.dev/oneloyalty-service"

  SFTP_DELAY_TIME: "60000"

  APP_PERFORMANCE_THREADS: "8"
  APP_PERFORMANCE_QUEUES: "2048"

  JAVA_OPT: |
    -javaagent:/monitor/opentelemetry-javaagent.jar -Dotel.service.name=$SERVICE_NAME -Dotel.metrics.exporter=none -Dotel.exporter.otlp.protocol=grpc -Dotel.resource.attributes=deployment.environment=${ENVIRONMENT} -Dotel.semconv-stability.opt-in=http -Dotel.instrumentation.micrometer.base-time-unit=s -Dotel.instrumentation.log4j-appender.experimental-log-attributes=true -Dotel.instrumentation.logback-appender.experimental-log-attributes=true -Dotel.exporter.otlp.endpoint=http://grafana-agent.monitoring.svc.cluster.local:4317
