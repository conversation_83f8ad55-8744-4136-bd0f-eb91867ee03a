apiVersion: apps/v1
kind: Deployment
metadata:
  name: ${CI_PROJECT_NAME}
  namespace: ${NAMESPACE}
spec:
  replicas: 1
  progressDeadlineSeconds: 120
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      name: ${CI_PROJECT_NAME}
  template:
    metadata:
      name: ${CI_PROJECT_NAME}
      labels:
        name: ${CI_PROJECT_NAME}
    spec:
      containers:
        - name: ${CI_PROJECT_NAME}
          image: asia.gcr.io/vinid-devops/${CI_PROJECT_NAME}:${IMAGE_TAG}
          ports:
            - name: http
              containerPort: 8080
          envFrom:
            - configMapRef:
                name: ${CI_PROJECT_NAME}
            - secretRef:
                name: ${CI_PROJECT_NAME}
          volumeMounts:
            - name: oneloyalty-kafka-cert
              mountPath: "/kafka"
              readOnly: true
      volumes:
        - name: oneloyalty-kafka-cert
          secret:
            secretName: oneloyalty-kafka-cert
      restartPolicy: Always
      imagePullSecrets:
        - name: docker-image-pull-secret
