apiVersion: v1
kind: ConfigMap
metadata:
  name: ${CI_PROJECT_NAME}
data:
  ENVIRONMENT: "${ENVIRONMENT}"
  APP_BUSINESS_CODE: "TCB"
  APP_PROGRAM_CODE: "TCBC"
  SFTP_HOST: "3rd-party-mft-test-nlb-c490268345ce2cde.elb.ap-southeast-1.amazonaws.com"
  SFTP_PORT: "9122"
  SFTP_RETRY_HOST: "**************"
  SFTP_RETRY_PORT: "2022"
  SFTP_BASE_FOLDER: "/Download/PFA_Loyalty"
  SFTP_RETRY_BASE_FOLDER: "/reconcile/loyalty-reward/uat/retry/read/autoearning"
  SFTP_UO_OUTPUT_FOLDER: "/Upload/PFA_Loyalty/UO"
  SFTP_UT_OUTPUT_FOLDER: "/loyalty-reward/autoearning"
  SFTP_PRIVATE_KEY_PATH: "keys/ssh_private_key.pem"
  SFTP_PGP_PRIVATE_KEY_PATH: "keys/pgp_private_key.pem"
  SFTP_PGP_PUBLIC_KEY_PATH: "keys/pfa-sign-public-key.pem"
  SFTP_PGP_PRIVATE_KEY_OUT_PATH: "keys/pgp_private_key.pem"
  SFTP_PGP_UO_PUBLIC_KEY_OUT_PATH: "keys/pfa-encrypt-public-key.pem"
  SFTP_PGP_UT_PUBLIC_KEY_OUT_PATH: "keys/encrypt_key_np.pem"
  SFTP_PGP_CONTROL_ENABLE: "false"
  SFTP_PGP_DATA_ENABLE: "true"
  SFTP_PGP_RETRY_CONTROL_ENABLE: "false"
  SFTP_PGP_RETRY_DATA_ENABLE: "false"
  KNOWN_HOSTS_PATH: "./conf/known_hosts"
  OL_SERVICE_BASE_URL: "https://api-uat.int.vinid.dev/oneloyalty-service"
  SFTP_DELAY_TIME: "10000"

  APP_PERFORMANCE_THREADS: "8"
  APP_PERFORMANCE_QUEUES: "2048"

  JAVA_OPT: |
    -javaagent:/monitor/opentelemetry-javaagent.jar -Dotel.service.name=$SERVICE_NAME -Dotel.metrics.exporter=none -Dotel.exporter.otlp.protocol=grpc -Dotel.resource.attributes=deployment.environment=${ENVIRONMENT} -Dotel.semconv-stability.opt-in=http -Dotel.instrumentation.micrometer.base-time-unit=s -Dotel.instrumentation.log4j-appender.experimental-log-attributes=true -Dotel.instrumentation.logback-appender.experimental-log-attributes=true -Dotel.exporter.otlp.endpoint=http://grafana-agent.monitoring.svc.cluster.local:4317
