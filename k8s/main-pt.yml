apiVersion: apps/v1
kind: Deployment
metadata:
  name: ${CI_PROJECT_NAME}
  namespace: ${NAMESPACE}
spec:
  replicas: 1
  progressDeadlineSeconds: 120
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      name: ${CI_PROJECT_NAME}
  template:
    metadata:
      name: ${CI_PROJECT_NAME}
      labels:
        name: ${CI_PROJECT_NAME}
    spec:
      containers:
        - name: ${CI_PROJECT_NAME}
          image: asia.gcr.io/vinid-devops/${CI_PROJECT_NAME}:${IMAGE_TAG}
          ports:
            - name: http
              containerPort: 8080
          envFrom:
            - configMapRef:
                name: ${CI_PROJECT_NAME}
            - secretRef:
                name: ${CI_PROJECT_NAME}
      restartPolicy: Always
      imagePullSecrets:
        - name: docker-image-pull-secret
---
apiVersion: v1
kind: Service
metadata:
  labels:
    name: ${CI_PROJECT_NAME}
  name: ${CI_PROJECT_NAME}
  namespace: ${NAMESPACE}
spec:
  ports:
    - name: http
      port: 80
      targetPort: 8080
  selector:
    name: ${CI_PROJECT_NAME}
  type: ClusterIP
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: ${CI_PROJECT_NAME}
  namespace: ${NAMESPACE}
spec:
  behavior:
    scaleDown:
      policies:
        - periodSeconds: 15
          type: Percent
          value: 100
      selectPolicy: Max
      stabilizationWindowSeconds: 10
    scaleUp:
      policies:
        - periodSeconds: 15
          type: Percent
          value: 100
        - periodSeconds: 15
          type: Pods
          value: 1
      selectPolicy: Max
      stabilizationWindowSeconds: 30
  maxReplicas: ${MAX_REPLICA}
  metrics:
    - resource:
        name: cpu
        target:
          averageUtilization: ${CPU_TARGET_PERCENT}
          type: Utilization
      type: Resource
  minReplicas: ${MIN_REPLICA}
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: ${CI_PROJECT_NAME}
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
  name: ${CI_PROJECT_NAME}
spec:
  rules:
    - host: "$HOST"
      http:
        paths:
          - pathType: Prefix
            path: ${SERVICE_PATH}
            backend:
              service:
                name: ${CI_PROJECT_NAME}
                port:
                  number: 80
