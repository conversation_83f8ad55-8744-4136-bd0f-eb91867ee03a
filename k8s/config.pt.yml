apiVersion: v1
kind: ConfigMap
metadata:
  name: ${CI_PROJECT_NAME}
data:
  ENVIRONMENT: "${ENVIRONMENT}"
  APP_BUSINESS_CODE: "TCB"
  APP_PROGRAM_CODE: "TCBC"
  SFTP_HOST: "**************"
  SFTP_PORT: "2022"
  SFTP_BASE_FOLDER: "/reconcile/pt/loyalty-reward/read/autoearning"
  SFTP_UO_OUTPUT_FOLDER: "/reconcile/pt/loyalty-reward/write/autoearning/UO"
  SFTP_UT_OUTPUT_FOLDER: "/reconcile/pt/loyalty-reward/write/autoearning/UT"
  KNOWN_HOSTS_PATH: "./conf/known_hosts"
  SFTP_PGP_ENABLE: "false"
  OL_SERVICE_BASE_URL: "http://oneloyalty-service/oneloyalty-service"

  APP_PERFORMANCE_THREADS: "40"
  APP_PERFORMANCE_QUEUES: "1024"

  SFTP_DELAY_TIME: "300000"

  JAVA_OPT: |
    -Xms1024m -Xmx1024m
    -javaagent:/monitor/opentelemetry-javaagent.jar -Dotel.service.name=$CI_PROJECT_NAME -Dotel.metrics.exporter=none -Dotel.exporter.otlp.protocol=grpc -Dotel.resource.attributes=deployment.environment=${ENVIRONMENT} -Dotel.semconv-stability.opt-in=http -Dotel.instrumentation.micrometer.base-time-unit=s -Dotel.instrumentation.log4j-appender.experimental-log-attributes=true -Dotel.instrumentation.logback-appender.experimental-log-attributes=true -Dotel.exporter.otlp.endpoint=http://grafana-agent.monitoring.svc.cluster.local:4317
    -javaagent:/monitor/newrelic/newrelic.jar -Dnewrelic.environment=${ENVIRONMENT} -Dnewrelic.config.file=monitor/newrelic.yml
 
