common: &default_settings
  license_key: '${NEW_RELIC_LICENSE_KEY}'
  agent_enabled: true
  app_name: ONELOYALTY-SERVICE
  high_security: false
  enable_auto_app_naming: false
  enable_auto_transaction_naming: true
  log_level: off
  audit_mode: false
  log_file_count: 1
  log_limit_in_kbytes: 0
  log_daily: false
  log_file_name: newrelic_agent.log
  application_logging:
    enabled: false
  # proxy_host: **************
  # proxy_port: 9090
  #proxy_user: username
  #proxy_password: password
  #proxy_scheme: https
  max_stack_trace_lines: 30
  attributes:
    enabled: true
  transaction_tracer:
    enabled: true
    transaction_threshold: apdex_f
    record_sql: obfuscated
    log_sql: true
    stack_trace_threshold: 0.5
    explain_enabled: true
    explain_threshold: 0.5
    top_n: 20
  error_collector:
    enabled: true
    ignore_errors: akka.actor.ActorKilledException
    ignore_status_codes: 404
    ignore_classes:
      - "com.oneid.oneloyalty.common.exception.SystemException"
      - "com.oneid.oneloyalty.common.exception.BusinessException"
  transaction_events:
    enabled: true
    max_samples_stored: 2000
  distributed_tracing:
    enabled: true
  cross_application_tracer:
    enabled: true
  thread_profiler:
    enabled: true
  browser_monitoring:
    auto_instrument: true
  class_transformer:
    com.newrelic.instrumentation.servlet-user:
      enabled: false
    com.newrelic.instrumentation.spring-aop-2:
      enabled: false
    com.newrelic.instrumentation.jdbc-resultset:
      enabled: false
    classloader_excludes:
      groovy.lang.GroovyClassLoader$InnerLoader,
      org.codehaus.groovy.runtime.callsite.CallSiteClassLoader,
      com.collaxa.cube.engine.deployment.BPELClassLoader,
      org.springframework.data.convert.ClassGeneratingEntityInstantiator$ObjectInstantiatorClassGenerator,
      org.mvel2.optimizers.impl.asm.ASMAccessorOptimizer$ContextClassLoader,
      gw.internal.gosu.compiler.SingleServingGosuClassLoader,

  # User-configurable custom labels for this agent.  Labels are name-value pairs.
  # There is a maximum of 64 labels per agent.  Names and values are limited to 255 characters.
  # Names and values may not contain colons (:) or semicolons (;).
  labels:

  # An example label
  #label_name: label_value


# Application Environments
# ------------------------------------------
# Environment specific settings are in this section.
# You can use the environment to override the default settings.
# For example, to change the app_name setting.
# Use -Dnewrelic.environment=<environment> on the Java startup command line
# to set the environment.
# The default environment is production.

# NOTE if your application has other named environments, you should
# provide configuration settings for these environments here.
dev:
  <<: *default_settings
  app_name: oneloyalty-service-dev

qc:
  <<: *default_settings
  app_name: oneloyalty-service-qc

uat:
  <<: *default_settings
  app_name: ps2-auto-earning-worker-uat

pt:
  <<: *default_settings
  app_name: ps2-auto-earning-worker-pt

stg:
  <<: *default_settings
  app_name: ps2-auto-earning-worker-stg

production:
  <<: *default_settings
  app_name: ps2-auto-earning-worker
