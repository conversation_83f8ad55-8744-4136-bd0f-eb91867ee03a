FROM openjdk:17-jdk
EXPOSE 8080

ENV JVM_XMX 1024m
COPY bin/entrypoint.sh /entrypoint.sh
COPY src/main/resources/* /conf/
COPY compiled/app.jar /app.jar

RUN apt update && \
    apt install -y curl unzip

RUN mkdir -p /app \
   && curl -Lo /app/opentelemetry-javaagent.jar https://github.com/open-telemetry/opentelemetry-java-instrumentation/releases/download/v1.27.0/opentelemetry-javaagent.jar \
  && curl -Lo /app/newrelic.zip https://download.newrelic.com/newrelic/java-agent/newrelic-agent/current/newrelic-java.zip \
  && unzip /app/newrelic.zip -d /app/                                                                            \
  && rm /app/newrelic.zip

COPY newrelic/newrelic.yml /app

ENTRYPOINT ["/entrypoint.sh"]
